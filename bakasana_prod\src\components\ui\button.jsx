
'use client';
import * as React from "react";

import { cva  } from "class-variance-authority";

import { cn  } from "@/lib/utils";

export { Button, buttonVariants }
import { Slot  } from "@radix-ui/react-slot";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-light transition-opacity duration-300 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-offset-1 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        // BAKASANA Ghost Buttons - Ultra-minimalist
        primary: "bg-transparent border border-charcoal text-charcoal hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4",
        secondary: "bg-transparent border border-stone text-stone hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4",
        accent: "bg-transparent border border-charcoal-gold text-charcoal-gold hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4",
        ghost: "bg-transparent text-charcoal hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4",
        // Legacy variants for compatibility
        outline: "bg-transparent border border-stone/20 text-charcoal hover:opacity-70",
        hero: "bg-transparent border border-stone/10 text-charcoal hover:opacity-70",
      },
      size: {
        sm: "px-hero-padding py-3 text-xs",
        default: "px-12 py-4 text-xs",
        lg: "px-16 py-5 text-sm",
        icon: "h-9 w-9 px-0",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
    },
  }
)

const Button = React.forwardRef(({ 
  className, 
  variant, 
  size, 
  asChild = false, 
  ...props 
}, ref) => {
  const Comp = asChild ? Slot : "button"
  return (
    <Comp
      className={cn(buttonVariants({ variant, size, className }))}
      ref={ref}
      {...props}
    />
  )
})
Button.displayName = "Button"

