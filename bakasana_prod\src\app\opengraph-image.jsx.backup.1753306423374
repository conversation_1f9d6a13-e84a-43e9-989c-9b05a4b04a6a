import { ImageResponse } from 'next/og';

export const alt = 'BAKASANA - Najlepsze Retreaty Jogi na Bali i Sri Lanka 2025';
export const size = {
  width: 1200,
  height: 630,
};
export const contentType = 'image/png';

export default async function OpenGraphImage() {
  return new ImageResponse(
    (
      <div
        style={{
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#FDFCF8',
          backgroundImage: 'radial-gradient(circle at 25% 25%, #C9A575 0%, transparent 50%), radial-gradient(circle at 75% 75%, #D4AF37 0%, transparent 50%)',
          fontFamily: 'serif',
          position: 'relative',
        }}
      >
        {/* Background Pattern */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: 'repeating-linear-gradient(45deg, #C9A575 0px, #C9A575 1px, transparent 1px, transparent 20px)',
            opacity: 0.1,
          }}
        />
        
        {/* Om Symbol */}
        <div
          style={{
            position: 'absolute',
            top: '50px',
            fontSize: '40px',
            color: '#C9A575',
            opacity: 0.8,
          }}
        >
          ॐ
        </div>

        {/* Main Content */}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center',
            padding: '40px',
          }}
        >
          {/* Title */}
          <h1
            style={{
              fontSize: '72px',
              fontWeight: 200,
              color: '#3A3A3A',
              letterSpacing: '0.25em',
              marginBottom: '20px',
              fontFamily: 'serif',
              textTransform: 'uppercase',
            }}
          >
            BAKASANA
          </h1>

          {/* Subtitle */}
          <p
            style={{
              fontSize: '28px',
              color: '#C9A575',
              fontWeight: 300,
              marginBottom: '30px',
              letterSpacing: '0.1em',
            }}
          >
            Retreaty Jogi • Bali & Sri Lanka
          </p>

          {/* Quote */}
          <p
            style={{
              fontSize: '20px',
              color: '#5A5A5A',
              fontStyle: 'italic',
              marginBottom: '40px',
              fontFamily: 'serif',
            }}
          >
            — gdzie sens spotyka swój rytm —
          </p>

          {/* Description */}
          <p
            style={{
              fontSize: '18px',
              color: '#3A3A3A',
              lineHeight: 1.4,
              maxWidth: '800px',
              textAlign: 'center',
            }}
          >
            Odkryj transformacyjne retreaty jogi w duchowym sercu Azji z certyfikowaną instruktorką Julią Jakubowicz. Małe grupy, luksusowe hotele, daily joga, medytacja i ayurveda.
          </p>
        </div>

        {/* Bottom Elements */}
        <div
          style={{
            position: 'absolute',
            bottom: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
            padding: '0 40px',
          }}
        >
          {/* Website */}
          <div
            style={{
              fontSize: '16px',
              color: '#8A8A8A',
              fontFamily: 'sans-serif',
            }}
          >
            bakasana-travel.blog
          </div>

          {/* Year */}
          <div
            style={{
              fontSize: '16px',
              color: '#C9A575',
              fontFamily: 'sans-serif',
              fontWeight: 600,
            }}
          >
            2025
          </div>
        </div>

        {/* Decorative Elements */}
        <div
          style={{
            position: 'absolute',
            top: '20px',
            right: '20px',
            width: '60px',
            height: '60px',
            backgroundColor: '#C9A575',
            opacity: 0.3,
          }}
        />
        <div
          style={{
            position: 'absolute',
            bottom: '20px',
            left: '20px',
            width: '40px',
            height: '40px',
            backgroundColor: '#D4AF37',
            opacity: 0.3,
          }}
        />
      </div>
    ),
    {
      ...size,
    }
  );
}