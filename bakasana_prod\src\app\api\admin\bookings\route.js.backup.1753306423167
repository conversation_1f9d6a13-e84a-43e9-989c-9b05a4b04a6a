import { NextResponse } from 'next/server';

import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// Tymczasowe przechowywanie rezerwacji (w produkcji użyj bazy danych)
let bookings = [
  {
    id: '1',
    firstName: 'Anna',
    lastName: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+48 123 456 789',
    program: 'Retreat Jogi',
    destination: 'Bali',
    status: 'pending',
    createdAt: new Date('2024-12-15T10:30:00Z').toISOString(),
    message: '<PERSON><PERSON><PERSON> się cieszę na ten retreat!'
  },
  {
    id: '2',
    firstName: 'Marcin',
    lastName: 'Nowak',
    email: '<EMAIL>',
    phone: '+48 987 654 321',
    program: 'Ayurveda & Joga',
    destination: 'Sri Lanka',
    status: 'confirmed',
    createdAt: new Date('2024-12-14T14:15:00Z').toISOString(),
    message: '<PERSON><PERSON> m<PERSON>ę dostać informacje o diecie?'
  },
  {
    id: '3',
    firstName: '<PERSON>arzyna',
    lastName: 'Wi<PERSON>niewska',
    email: '<EMAIL>',
    phone: '+48 555 123 456',
    program: 'Detox & Mindfulness',
    destination: 'Tajlandia',
    status: 'pending',
    createdAt: new Date('2024-12-13T09:45:00Z').toISOString(),
    message: 'Pierwszy raz na takim retrecie, jestem podekscytowana!'
  }
];

// Funkcja weryfikacji tokenu admin
function verifyAdminToken(request) {
  const authHeader = request.headers.get('authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { valid: false, error: 'Brak tokenu autoryzacji' };
  }

  const token = authHeader.substring(7);

  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'bakasana-travel-admin',
      audience: 'bakasana-travel-app'
    });

    if (decoded.role !== 'admin') {
      return { valid: false, error: 'Niewystarczające uprawnienia' };
    }

    return { valid: true, user: decoded };
  } catch (error) {
    return { valid: false, error: 'Nieprawidłowy token' };
  }
}

// GET - Pobierz wszystkie rezerwacje
export async function GET(request) {
  try {
    // Weryfikuj token admin
    const authResult = verifyAdminToken(request);
    if (!authResult.valid) {
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }

    // Sortuj rezerwacje według daty (najnowsze pierwsze)
    const sortedBookings = [...bookings].sort((a, b) => 
      new Date(b.createdAt) - new Date(a.createdAt)
    );

    return NextResponse.json({
      success: true,
      bookings: sortedBookings,
      total: bookings.length,
      stats: {
        pending: bookings.filter(b => b.status === 'pending').length,
        confirmed: bookings.filter(b => b.status === 'confirmed').length,
        cancelled: bookings.filter(b => b.status === 'cancelled').length
      }
    });

  } catch (error) {
    console.error('Admin bookings GET error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Błąd serwera podczas pobierania rezerwacji',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// POST - Dodaj nową rezerwację (opcjonalnie, dla testów)
export async function POST(request) {
  try {
    // Weryfikuj token admin
    const authResult = verifyAdminToken(request);
    if (!authResult.valid) {
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }

    const bookingData = await request.json();

    // Walidacja danych
    const requiredFields = ['firstName', 'lastName', 'email', 'program', 'destination'];
    for (const field of requiredFields) {
      if (!bookingData[field]) {
        return NextResponse.json(
          { success: false, error: `Pole ${field} jest wymagane` },
          { status: 400 }
        );
      }
    }

    // Utwórz nową rezerwację
    const newBooking = {
      id: Date.now().toString(),
      ...bookingData,
      status: bookingData.status || 'pending',
      createdAt: new Date().toISOString()
    };

    bookings.push(newBooking);

    console.log(`New booking created by admin: ${newBooking.id}`);

    return NextResponse.json({
      success: true,
      booking: newBooking,
      message: 'Rezerwacja została dodana pomyślnie'
    });

  } catch (error) {
    console.error('Admin bookings POST error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Błąd serwera podczas dodawania rezerwacji',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
