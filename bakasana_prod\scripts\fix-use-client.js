#!/usr/bin/env node

/**
 * BAKASANA - Fix "use client" directive placement
 * Fixes files where "use client" is not at the top
 */

const fs = require('fs');
const path = require('path');
const FileProcessor = require('./utils/FileProcessor');
const Logger = require('./utils/Logger');

class UseClientFixer extends FileProcessor {
  constructor() {
    super();
    this.logger = new Logger('UseClientFixer', { colors: true });
    this.fixes = [];
  }

  /**
   * Fix "use client" placement in a file
   */
  fixUseClient(filePath, content) {
    const lines = content.split('\n');
    let useClientIndex = -1;
    let exportDefaultIndex = -1;
    let firstImportIndex = -1;
    
    // Find relevant lines
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (line === "'use client';" || line === '"use client";') {
        useClientIndex = i;
      }
      
      if (line.startsWith('export default ') && exportDefaultIndex === -1) {
        exportDefaultIndex = i;
      }
      
      if (line.startsWith('import ') && firstImportIndex === -1) {
        firstImportIndex = i;
      }
    }
    
    // If no "use client" found, nothing to fix
    if (useClientIndex === -1) {
      return null;
    }
    
    // If "use client" is already at the top (before imports), nothing to fix
    if (useClientIndex === 0 || (firstImportIndex > -1 && useClientIndex < firstImportIndex)) {
      return null;
    }
    
    // Remove "use client" from current position
    const useClientLine = lines[useClientIndex];
    lines.splice(useClientIndex, 1);
    
    // Remove export default if it's misplaced
    if (exportDefaultIndex > -1 && exportDefaultIndex < useClientIndex) {
      const exportLine = lines[exportDefaultIndex];
      lines.splice(exportDefaultIndex, 1);
      
      // Add export at the end
      lines.push('');
      lines.push(exportLine);
    }
    
    // Add "use client" at the top
    lines.unshift(useClientLine);
    lines.unshift('');
    
    this.fixes.push({
      file: filePath,
      type: 'use-client-placement',
      description: 'Moved "use client" directive to top of file'
    });
    
    return lines.join('\n');
  }

  /**
   * Process all files and fix "use client" placement
   */
  async fixAllUseClient() {
    this.logger.section('Use Client Directive Fix');
    
    const files = this.getAllFiles(this.srcPath, ['.js', '.jsx', '.ts', '.tsx']);
    this.logger.info(`Processing ${files.length} files...`);

    const results = await this.processFiles(files, async (filePath, content) => {
      const fixedContent = this.fixUseClient(filePath, content);
      
      if (fixedContent) {
        this.safeWriteFile(filePath, fixedContent);
        return 'fixed';
      }
      
      return 'no-changes';
    }, {
      onProgress: (current, total, file) => {
        this.logger.progress(current, total, path.relative(this.srcPath, file));
      }
    });

    // Generate report
    this.generateReport(results);
  }

  /**
   * Generate fix report
   */
  generateReport(results) {
    const stats = this.getStats();
    const fixedFiles = results.filter(r => r.result === 'fixed').length;
    
    this.logger.subsection('Fix Results');
    this.logger.info(`Files processed: ${stats.totalFiles}`);
    this.logger.info(`Files fixed: ${fixedFiles}`);
    this.logger.info(`Success rate: ${stats.successRate}%`);
    
    if (this.fixes.length > 0) {
      this.logger.subsection('Applied Fixes');
      
      this.fixes.forEach(fix => {
        this.logger.info(`✓ ${path.relative(this.projectRoot, fix.file)}`);
      });
    }
    
    // Save detailed report
    const reportPath = path.join(this.projectRoot, 'use-client-fix-report.json');
    const report = {
      timestamp: new Date().toISOString(),
      stats,
      fixes: this.fixes,
      summary: {
        totalFiles: stats.totalFiles,
        fixedFiles,
        successRate: stats.successRate
      }
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    this.logger.success(`Report saved to: ${reportPath}`);
  }
}

// Run if called directly
if (require.main === module) {
  const fixer = new UseClientFixer();
  fixer.fixAllUseClient().catch(console.error);
}

module.exports = UseClientFixer;
