'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, X } from 'lucide-react';
import { LuxuryFAB, LuxuryTooltip } from '@/components/LuxuryElements';

/**
 * LuxuryWhatsApp - Luksusowy floating WhatsApp button
 * Z subtelną animacją i eleganckim tooltipem
 */

export default function LuxuryWhatsApp({ 
  phoneNumber = "48123456789",
  message = "Cześć! Chciałabym dowiedzieć się więcej o retreatach BAKASANA 🧘‍♀️",
  position = "bottom-right"
}) {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    // Pokaż przycisk po 3 sekundach
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const handleWhatsAppClick = () => {
    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-8 right-8 z-50">
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="mb-4 bg-sanctuary rounded-2xl shadow-premium-shadow border border-enterprise-brown/10 p-6 max-w-sm"
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-br from-enterprise-brown to-terra rounded-full flex items-center justify-center mr-3">
                  <MessageCircle className="w-5 h-5 text-sanctuary" />
                </div>
                <div>
                  <h4 className="font-cormorant text-lg text-charcoal font-medium">
                    BAKASANA
                  </h4>
                  <p className="text-xs text-sage">
                    Zazwyczaj odpowiadamy w ciągu godziny
                  </p>
                </div>
              </div>
              <button
                onClick={handleToggleExpanded}
                className="text-sage hover:text-charcoal transition-colors duration-200"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            {/* Message */}
            <div className="mb-4">
              <p className="text-sm text-charcoal-light leading-relaxed">
                Cześć! 👋 Masz pytania o nasze retreaty? Napisz do nas - chętnie pomożemy!
              </p>
            </div>

            {/* CTA Button */}
            <motion.button
              onClick={handleWhatsAppClick}
              className="w-full bg-gradient-to-r from-enterprise-brown to-terra text-sanctuary py-3 px-4 rounded-xl font-inter font-medium text-sm transition-all duration-300 hover:shadow-lg hover:from-terra hover:to-sand"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Napisz na WhatsApp
            </motion.button>

            {/* Decorative element */}
            <div className="absolute -bottom-2 right-8 w-4 h-4 bg-sanctuary border-r border-b border-enterprise-brown/10 transform rotate-45" />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main FAB */}
      <LuxuryTooltip 
        content="Napisz do nas na WhatsApp" 
        position="left"
        goldAccent={true}
      >
        <motion.div
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ 
            type: "spring", 
            stiffness: 400, 
            damping: 17,
            delay: 0.2 
          }}
        >
          <LuxuryFAB
            onClick={isExpanded ? handleToggleExpanded : handleWhatsAppClick}
            className="relative overflow-hidden group"
            goldAccent={true}
          >
            {/* Pulsing ring */}
            <motion.div
              className="absolute inset-0 rounded-full border-2 border-enterprise-brown/30"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.7, 0, 0.7]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />

            {/* Icon with rotation animation */}
            <motion.div
              animate={{ rotate: isExpanded ? 45 : 0 }}
              transition={{ duration: 0.3 }}
            >
              {isExpanded ? (
                <X className="w-6 h-6" />
              ) : (
                <MessageCircle className="w-6 h-6" />
              )}
            </motion.div>

            {/* Subtle shine effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12"
              initial={{ x: '-100%' }}
              animate={{ x: '100%' }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatDelay: 3,
                ease: "easeInOut"
              }}
            />
          </LuxuryFAB>
        </motion.div>
      </LuxuryTooltip>

      {/* Online indicator */}
      <motion.div
        className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-sanctuary"
        animate={{
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  );
}

// Wariant kompaktowy bez rozwijania
export function CompactWhatsApp({ phoneNumber, message }) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleClick = () => {
    const encodedMessage = encodeURIComponent(message || "Cześć! Chciałabym dowiedzieć się więcej o BAKASANA 🧘‍♀️");
    const whatsappUrl = `https://wa.me/${phoneNumber || "48123456789"}?text=${encodedMessage}`;
    window.open(whatsappUrl, '_blank');
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ 
        type: "spring", 
        stiffness: 400, 
        damping: 17,
        delay: 0.2 
      }}
      className="fixed bottom-8 right-8 z-50"
    >
      <LuxuryFAB
        onClick={handleClick}
        className="relative group"
        goldAccent={true}
      >
        <MessageCircle className="w-6 h-6" />
        
        {/* Pulsing effect */}
        <motion.div
          className="absolute inset-0 rounded-full bg-enterprise-brown/20"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.5, 0, 0.5]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </LuxuryFAB>
    </motion.div>
  );
}