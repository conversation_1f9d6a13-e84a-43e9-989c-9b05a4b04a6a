'use client';

import React from 'react';
import Image from 'next/image';
import { SectionTitle, BodyText, LeadText, CardTitle } from '@/components/ui/UnifiedTypography';
import { ServiceCard } from '@/components/ui/UnifiedCard';
import UnifiedButton, { CTAButton, SecondaryButton } from '@/components/ui/UnifiedButton';
import { Icon } from '@/components/ui/IconSystem';
import FitsseyIntegration, { FitsseyFloatingButton, FitsseyWidget } from '@/components/FitsseyIntegration';
import SEOBreadcrumbs from '@/components/SEOBreadcrumbs';
import InternalLinks from '@/components/InternalLinks';
import LocalBusinessSchema from '@/components/SEO/LocalBusinessSchema';

export default function ZajeciaStacjonarnePage() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Zajęcia Jo<PERSON>acjon<PERSON>ne - <PERSON>",
    "description": "Zajęcia jogi i fitness w studio w Warszawie. Zapisy online przez system Fitssey.",
    "provider": {
      "@type": "Person",
      "name": "<PERSON>"
    },
    "areaServed": "Warszawa",
    "serviceType": "Zajęcia fitness i jogi"
  };

  const classTypes = [
    {
      title: "Hatha Yoga",
      description: "Spokojne, medytacyjne zajęcia skupione na precyzyjnym wykonywaniu asan i pracy z oddechem.",
      duration: "75 min",
      level: "Wszystkie poziomy",
      benefits: ["Poprawa elastyczności", "Redukcja stresu", "Wzmocnienie koncentracji"]
    },
    {
      title: "Vinyasa Flow",
      description: "Dynamiczne zajęcia łączące ruch z oddechem w płynnych sekwencjach.",
      duration: "60 min", 
      level: "Średniozaawansowany",
      benefits: ["Wzmocnienie mięśni", "Poprawa kondycji", "Zwiększenie elastyczności"]
    },
    {
      title: "Yin Yoga",
      description: "Głęboko relaksujące zajęcia z długim utrzymywaniem pozycji w pozycjach siedzącej i leżącej.",
      duration: "90 min",
      level: "Wszystkie poziomy", 
      benefits: ["Głęboka relaksacja", "Poprawa elastyczności", "Regeneracja"]
    },
    {
      title: "Yoga Terapeutyczna",
      description: "Zajęcia dostosowane do potrzeb osób z kontuzjami i ograniczeniami ruchowymi.",
      duration: "60 min",
      level: "Terapeutyczny",
      benefits: ["Rehabilitacja", "Łagodzenie bólu", "Poprawa mobilności"]
    }
  ];

  const scheduleInfo = [
    { day: "Poniedziałek", time: "18:00", type: "Hatha Yoga" },
    { day: "Wtorek", time: "19:30", type: "Vinyasa Flow" },
    { day: "Środa", time: "17:00", type: "Yoga Terapeutyczna" },
    { day: "Czwartek", time: "19:00", type: "Yin Yoga" },
    { day: "Piątek", time: "18:30", type: "Vinyasa Flow" },
    { day: "Sobota", time: "10:00", type: "Hatha Yoga" },
    { day: "Niedziela", time: "11:00", type: "Yin Yoga" }
  ];



  return (
    <>
      <LocalBusinessSchema 
        businessType="YogaStudio"
        location="Warszawa"
      />
      
      <SEOBreadcrumbs 
        customBreadcrumbs={[
          { href: '/zajecia-stacjonarne', label: 'Zajęcia Stacjonarne' }
        ]}
      />

      <main className="min-h-screen bg-sanctuary">
        {/* HERO SECTION */}
        <section className="py-section bg-gradient-to-b from-whisper to-sanctuary">
          <div className="container mx-auto px-container-sm">
            <div className="max-w-4xl mx-auto text-center">
              <SectionTitle className="mb-md">
                Zajęcia Stacjonarne
              </SectionTitle>
              <LeadText className="mb-lg">
                Dołącz do naszych zajęć jogi i fitness w przytulnym studio w Warszawie. 
                Małe grupy, indywidualne podejście i profesjonalna opieka instruktora.
              </LeadText>
              
              <div className="flex flex-col sm:flex-row gap-sm justify-center mb-xl">
                <FitsseyIntegration
                  buttonText="Zapisz się online"
                  variant="primary"
                  size="lg"
                  trackingEvent="hero_signup"
                />
                <SecondaryButton 
                  href="#harmonogram"
                  className="px-8 py-4"
                >
                  Zobacz harmonogram
                </SecondaryButton>
              </div>

              <div className="bg-white/50 backdrop-blur-sm p-6 rounded-lg inline-block">
                <BodyText className="text-sm text-sage">
                  📍 Studio: ul. Przykładowa 123, Warszawa<br/>
                  📞 Kontakt: +48 606 101 523<br/>
                  💳 Zapisy przez system Fitssey
                </BodyText>
              </div>
            </div>
          </div>
        </section>

        {/* RODZAJE ZAJĘĆ */}
        <section className="py-section bg-linen">
          <div className="container mx-auto px-container-sm">
            <div className="text-center mb-2xl">
              <SectionTitle>
                Rodzaje Zajęć
              </SectionTitle>
              <BodyText className="text-sage max-w-2xl mx-auto">
                Oferujemy różnorodne style jogi dostosowane do każdego poziomu zaawansowania
              </BodyText>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-lg max-w-6xl mx-auto">
              {classTypes.map((classType, index) => (
                <ServiceCard key={index} className="h-full">
                  <div className="flex justify-between items-start mb-sm">
                    <CardTitle className="text-enterprise-brown">
                      {classType.title}
                    </CardTitle>
                    <div className="text-right text-sm text-sage">
                      <div>{classType.duration}</div>
                      <div>{classType.level}</div>
                    </div>
                  </div>
                  
                  <BodyText className="text-charcoal-light mb-md">
                    {classType.description}
                  </BodyText>
                  
                  <div>
                    <h4 className="text-sm font-medium text-enterprise-brown mb-2">
                      Korzyści:
                    </h4>
                    <ul className="space-y-1">
                      {classType.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-center text-sm text-charcoal-light">
                          <Icon name="check" size="xs" className="text-enterprise-brown mr-2" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                </ServiceCard>
              ))}
            </div>
          </div>
        </section>

        {/* HARMONOGRAM */}
        <section id="harmonogram" className="py-section bg-sanctuary">
          <div className="container mx-auto px-container-sm">
            <div className="text-center mb-2xl">
              <SectionTitle>
                Harmonogram Zajęć
              </SectionTitle>
              <BodyText className="text-sage max-w-2xl mx-auto">
                Regularne zajęcia przez cały tydzień. Zapisz się na wybrane terminy przez system Fitssey.
              </BodyText>
            </div>

            <div className="max-w-4xl mx-auto">
              <div className="bg-white rounded-lg shadow-subtle overflow-hidden">
                <div className="grid grid-cols-1 md:grid-cols-7 gap-0">
                  {scheduleInfo.map((schedule, index) => (
                    <div 
                      key={index} 
                      className="p-6 border-b md:border-b-0 md:border-r border-stone-light last:border-r-0 last:border-b-0"
                    >
                      <div className="text-center">
                        <div className="text-sm font-medium text-enterprise-brown mb-2">
                          {schedule.day}
                        </div>
                        <div className="text-lg font-bold text-charcoal mb-2">
                          {schedule.time}
                        </div>
                        <div className="text-xs text-sage">
                          {schedule.type}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="text-center mt-lg">
                <FitsseyIntegration
                  buttonText="Zapisz się na zajęcia"
                  variant="primary"
                  size="lg"
                  trackingEvent="schedule_signup"
                />
              </div>
            </div>
          </div>
        </section>

        {/* SYSTEM ZAPISÓW */}
        <section className="py-section bg-whisper">
          <div className="container mx-auto px-container-sm">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-xl">
                <SectionTitle>
                  System Zapisów Fitssey
                </SectionTitle>
                <BodyText className="text-sage">
                  Używamy profesjonalnego systemu Fitssey do zarządzania zapisami i płatnościami. 
                  Bezpieczne, wygodne i dostępne 24/7.
                </BodyText>
              </div>

              {/* Informacje o Fitssey */}
              <div className="bg-enterprise-brown/5 p-6 rounded-lg mb-xl">
                <div className="flex items-start gap-4">
                  <Icon name="shield-check" size="lg" className="text-enterprise-brown mt-1 flex-shrink-0" />
                  <div>
                    <CardTitle className="mb-2 text-enterprise-brown">
                      Dlaczego Fitssey?
                    </CardTitle>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-charcoal-light">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Icon name="check" size="xs" className="text-enterprise-brown" />
                          Bezpieczne płatności online
                        </div>
                        <div className="flex items-center gap-2">
                          <Icon name="check" size="xs" className="text-enterprise-brown" />
                          Automatyczne potwierdzenia
                        </div>
                        <div className="flex items-center gap-2">
                          <Icon name="check" size="xs" className="text-enterprise-brown" />
                          Łatwe anulowanie zajęć
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Icon name="check" size="xs" className="text-enterprise-brown" />
                          Historia Twoich zajęć
                        </div>
                        <div className="flex items-center gap-2">
                          <Icon name="check" size="xs" className="text-enterprise-brown" />
                          Powiadomienia SMS/Email
                        </div>
                        <div className="flex items-center gap-2">
                          <Icon name="check" size="xs" className="text-enterprise-brown" />
                          Zarządzanie karnetami
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-lg mb-xl">
                <div className="text-center">
                  <div className="w-16 h-16 bg-enterprise-brown/10 rounded-full flex items-center justify-center mx-auto mb-md">
                    <Icon name="user-plus" size="lg" className="text-enterprise-brown" />
                  </div>
                  <CardTitle className="mb-sm">1. Utwórz konto</CardTitle>
                  <BodyText className="text-sage text-sm">
                    Zarejestruj się w systemie Fitssey jednym kliknięciem
                  </BodyText>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-enterprise-brown/10 rounded-full flex items-center justify-center mx-auto mb-md">
                    <Icon name="calendar" size="lg" className="text-enterprise-brown" />
                  </div>
                  <CardTitle className="mb-sm">2. Wybierz zajęcia</CardTitle>
                  <BodyText className="text-sage text-sm">
                    Przeglądaj dostępne terminy i wybierz odpowiadające Ci zajęcia
                  </BodyText>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-enterprise-brown/10 rounded-full flex items-center justify-center mx-auto mb-md">
                    <Icon name="credit-card" size="lg" className="text-enterprise-brown" />
                  </div>
                  <CardTitle className="mb-sm">3. Zapłać online</CardTitle>
                  <BodyText className="text-sage text-sm">
                    Bezpieczna płatność kartą lub BLIK bezpośrednio w systemie
                  </BodyText>
                </div>
              </div>

              <div className="bg-enterprise-brown/5 p-8 rounded-lg text-center">
                <CardTitle className="mb-md text-enterprise-brown">
                  Gotowy na rozpoczęcie praktyki?
                </CardTitle>
                <BodyText className="mb-lg text-charcoal-light">
                  Dołącz do naszej społeczności i odkryj transformacyjną moc jogi w przyjaznej atmosferze studia.
                </BodyText>
                <FitsseyIntegration
                  buttonText="Przejdź do systemu zapisów"
                  variant="primary"
                  size="lg"
                  trackingEvent="cta_signup"
                />
              </div>
            </div>
          </div>
        </section>

        {/* CENNIK I KARNETY */}
        <section className="py-section bg-linen">
          <div className="container mx-auto px-container-sm">
            <div className="text-center mb-2xl">
              <SectionTitle>
                Cennik i Karnety
              </SectionTitle>
              <BodyText className="text-sage max-w-2xl mx-auto">
                Elastyczne opcje płatności dostosowane do Twoich potrzeb
              </BodyText>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-lg max-w-5xl mx-auto">
              <ServiceCard className="text-center">
                <CardTitle className="text-enterprise-brown mb-sm">
                  Zajęcia Drop-in
                </CardTitle>
                <div className="text-3xl font-bold text-charcoal mb-sm">60 PLN</div>
                <BodyText className="text-sage mb-md">
                  Pojedyncze zajęcia bez zobowiązań
                </BodyText>
                <ul className="text-sm text-charcoal-light space-y-2">
                  <li>✓ Wszystkie rodzaje zajęć</li>
                  <li>✓ Rezerwacja online</li>
                  <li>✓ Anulowanie do 2h przed</li>
                </ul>
              </ServiceCard>

              <ServiceCard className="text-center border-2 border-enterprise-brown relative">
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-enterprise-brown text-white px-4 py-1 rounded-full text-xs">
                  NAJPOPULARNIEJSZY
                </div>
                <CardTitle className="text-enterprise-brown mb-sm">
                  Karnet 10 zajęć
                </CardTitle>
                <div className="text-3xl font-bold text-charcoal mb-sm">500 PLN</div>
                <BodyText className="text-sage mb-md">
                  Oszczędność 100 PLN
                </BodyText>
                <ul className="text-sm text-charcoal-light space-y-2">
                  <li>✓ Ważność 3 miesiące</li>
                  <li>✓ Wszystkie rodzaje zajęć</li>
                  <li>✓ Możliwość zamrożenia</li>
                  <li>✓ Priorytet w zapisach</li>
                </ul>
              </ServiceCard>

              <ServiceCard className="text-center">
                <CardTitle className="text-enterprise-brown mb-sm">
                  Karnet miesięczny
                </CardTitle>
                <div className="text-3xl font-bold text-charcoal mb-sm">350 PLN</div>
                <BodyText className="text-sage mb-md">
                  Nielimitowane zajęcia
                </BodyText>
                <ul className="text-sm text-charcoal-light space-y-2">
                  <li>✓ Bez limitu zajęć</li>
                  <li>✓ Wszystkie rodzaje zajęć</li>
                  <li>✓ Możliwość zamrożenia</li>
                  <li>✓ Zniżki na warsztaty</li>
                </ul>
              </ServiceCard>
            </div>

            <div className="text-center mt-xl">
              <BodyText className="text-sage mb-md">
                Wszystkie płatności realizowane są bezpiecznie przez system Fitssey
              </BodyText>
              <FitsseyIntegration
                buttonText="Zobacz pełny cennik"
                variant="secondary"
                size="md"
                trackingEvent="pricing_view"
              />
            </div>
          </div>
        </section>

        {/* KONSULTACJE INDYWIDUALNE */}
        <section id="konsultacje" className="py-section bg-whisper">
          <div className="container mx-auto px-container-sm">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-xl">
                <SectionTitle>
                  Konsultacje Indywidualne
                </SectionTitle>
                <BodyText className="text-sage">
                  Sesje terapeutyczne i konsultacje dostosowane do Twoich indywidualnych potrzeb
                </BodyText>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-lg mb-xl">
                <ServiceCard>
                  <CardTitle className="text-enterprise-brown mb-sm">
                    Konsultacja Terapeutyczna
                  </CardTitle>
                  <BodyText className="text-charcoal-light mb-md">
                    Indywidualna sesja z fizjoterapeutą i instruktorem jogi. Analiza postawy, 
                    plan ćwiczeń dostosowany do Twoich potrzeb i ograniczeń.
                  </BodyText>
                  <div className="text-sm text-sage space-y-1">
                    <div>⏱️ Czas trwania: 90 minut</div>
                    <div>💰 Cena: 200 PLN</div>
                    <div>📋 Zawiera plan ćwiczeń</div>
                  </div>
                </ServiceCard>

                <ServiceCard>
                  <CardTitle className="text-enterprise-brown mb-sm">
                    Sesja Jogi Indywidualnej
                  </CardTitle>
                  <BodyText className="text-charcoal-light mb-md">
                    Praktyka jogi dostosowana do Twojego poziomu i celów. Idealna dla osób 
                    z kontuzjami lub specjalnymi wymaganiami.
                  </BodyText>
                  <div className="text-sm text-sage space-y-1">
                    <div>⏱️ Czas trwania: 60 minut</div>
                    <div>💰 Cena: 150 PLN</div>
                    <div>🧘‍♀️ Pełna personalizacja</div>
                  </div>
                </ServiceCard>
              </div>

              <div className="text-center">
                <FitsseyIntegration
                  buttonText="Umów konsultację"
                  variant="primary"
                  size="lg"
                  trackingEvent="consultation_booking"
                />
              </div>
            </div>
          </div>
        </section>

        {/* KONTAKT I LOKALIZACJA */}
        <section className="py-section bg-sanctuary">
          <div className="container mx-auto px-container-sm">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-xl max-w-6xl mx-auto">
              <div>
                <SectionTitle className="mb-lg">
                  Znajdź nas
                </SectionTitle>
                
                <div className="space-y-md">
                  <div className="flex items-start gap-4">
                    <Icon name="map-pin" size="md" className="text-enterprise-brown mt-1" />
                    <div>
                      <CardTitle className="mb-2">Adres studia</CardTitle>
                      <BodyText className="text-charcoal-light">
                        ul. Przykładowa 123<br/>
                        00-001 Warszawa<br/>
                        (wejście od podwórka)
                      </BodyText>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <Icon name="phone" size="md" className="text-enterprise-brown mt-1" />
                    <div>
                      <CardTitle className="mb-2">Kontakt</CardTitle>
                      <BodyText className="text-charcoal-light">
                        Tel: +48 606 101 523<br/>
                        Email: <EMAIL>
                      </BodyText>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <Icon name="clock" size="md" className="text-enterprise-brown mt-1" />
                    <div>
                      <CardTitle className="mb-2">Godziny otwarcia</CardTitle>
                      <BodyText className="text-charcoal-light">
                        Pon-Pt: 17:00 - 21:00<br/>
                        Sob-Nie: 10:00 - 12:00<br/>
                        (zgodnie z harmonogramem zajęć)
                      </BodyText>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-subtle">
                <CardTitle className="mb-md text-center">
                  Masz pytania?
                </CardTitle>
                <BodyText className="text-sage text-center mb-lg">
                  Skontaktuj się z nami lub zapisz się bezpośrednio przez system Fitssey
                </BodyText>
                
                <div className="space-y-sm">
                  <FitsseyIntegration
                    buttonText="System zapisów Fitssey"
                    variant="primary"
                    size="md"
                    trackingEvent="contact_signup"
                    className="w-full"
                  />
                  
                  <SecondaryButton 
                    href="tel:+48606101523"
                    className="w-full py-3"
                  >
                    Zadzwoń: +48 606 101 523
                  </SecondaryButton>
                  
                  <SecondaryButton 
                    href="mailto:<EMAIL>"
                    className="w-full py-3"
                  >
                    Napisz email
                  </SecondaryButton>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Internal Links for SEO */}
        <InternalLinks currentPage="zajecia-stacjonarne" />

        {/* Floating Fitssey Button */}
        <FitsseyFloatingButton 
          position="bottom-right"
          message="Zapisz się na zajęcia"
        />
      </main>
    </>
  );
}