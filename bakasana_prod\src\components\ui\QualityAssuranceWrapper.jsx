'use client';

import { useEffect, useState  } from 'react';

export default function QualityAssuranceWrapper({ children }) {
  const [isClient, setIsClient] = useState(false);
  const [showQA, setShowQA] = useState(false);

  useEffect(() => {
    setIsClient(true);
    // Only show in development
    if (process.env.NODE_ENV === 'development') {
      setShowQA(true);
    }
  }, []);

  return (
    <>
      {children}
      {isClient && showQA && (
        <div className="fixed bottom-4 right-4 bg-charcoal text-silk p-4 rounded-lg shadow-lg z-50 max-w-sm">
          <h3 className="font-bold mb-2">Quality Monitor</h3>
          <p className="text-sm">Development mode active</p>
          <p className="text-xs text-gray-400">Quality assurance monitoring enabled</p>
        </div>
      )}
    </>
  );
}
