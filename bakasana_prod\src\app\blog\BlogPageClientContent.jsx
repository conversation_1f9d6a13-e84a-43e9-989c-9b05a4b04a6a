'use client';

import Link from 'next/link';
import Image from 'next/image';
import React, { useMemo, useState } from 'react';

// Magazine Style Blog Card
const PostCard = ({ post, featured = false, className = '' }) => {
  if (!post) return null;

  const cardClass = featured ? 'magazine-card-featured' : 'magazine-card';

  return (
    <article className={`${cardClass} ${className}`}>
      <Link href={`/blog/${post.slug || '#'}`} className="magazine-card-link">
        {/* IMAGE SECTION */}
        <div className="magazine-card-image">
          <div 
            className="magazine-image-bg"
            style={{
              backgroundImage: `url(${post.imageUrl || '/images/placeholder/image.jpg'})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            <div className="magazine-image-overlay">
              <span className="magazine-category">
                {post.category || 'Zapiski z podróży'}
              </span>
            </div>
          </div>
        </div>

        {/* CONTENT SECTION */}
        <div className="magazine-card-content">
          <h3 className="magazine-card-title">
            {post.title || 'Bez tytułu'}
          </h3>

          <p className="magazine-card-excerpt">
            {post.excerpt || ''}
          </p>

          <div className="magazine-card-footer">
            <span className="magazine-read-more">Czytaj więcej</span>
            {post.readTime && (
              <span className="magazine-read-time">{post.readTime} min</span>
            )}
          </div>
        </div>
      </Link>
    </article>
  );
};

export default function BlogPageClientContent({ posts = [] }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('wszystkie');

  const memoizedPosts = useMemo(() => {
    if (!Array.isArray(posts)) return [];
    return posts.filter(post => post && typeof post === 'object');
  }, [posts]);

  // Get unique categories
  const categories = useMemo(() => {
    const cats = ['wszystkie', ...new Set(memoizedPosts.map(post => post.category).filter(Boolean))];
    return cats;
  }, [memoizedPosts]);

  // Filter posts based on search and category
  const filteredPosts = useMemo(() => {
    let filtered = memoizedPosts;

    // Filter by category
    if (selectedCategory !== 'wszystkie') {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(post =>
        post.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    return filtered;
  }, [memoizedPosts, selectedCategory, searchTerm]);

  return (
    <div className="bg-sanctuary min-h-screen">
      {/* HERO SECTION - Magazine Style Header with Search */}
      <section className="magazine-hero">
        <div className="magazine-hero-content">
          <div className="magazine-header-line"></div>

          <h1 className="magazine-title">
            Zapiski z Podróży
          </h1>

          <p className="magazine-subtitle">
            Historie napisane sercem • Inspiracje z Azji • Praktyka jogi
          </p>

          {/* Search Bar */}
          <div className="mt-8 max-w-md mx-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="Szukaj artykułów..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-3 pr-10 bg-white/80 backdrop-blur-sm border border-enterprise-brown/20 text-charcoal placeholder-charcoal/60 focus:outline-none focus:border-enterprise-brown/40 transition-all duration-300"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-charcoal/40">
                <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          <div className="magazine-meta">
            {filteredPosts.length} {filteredPosts.length === 1 ? 'artykuł' : 'artykułów'}
            {searchTerm && ` dla "${searchTerm}"`}
          </div>

          <div className="magazine-header-line"></div>
        </div>
      </section>

      {/* CATEGORY FILTER */}
      <section className="py-6 bg-white/30 backdrop-blur-sm">
        <div className="max-w-6xl mx-auto px-hero-padding">
          <div className="flex flex-wrap justify-center gap-3">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 text-sm font-light tracking-wide transition-all duration-300 ${
                  selectedCategory === category
                    ? 'bg-enterprise-brown text-white shadow-sm'
                    : 'bg-white/80 text-charcoal hover:bg-enterprise-brown/10 border border-enterprise-brown/20'
                }`}
              >
                {category}
              </button>
            ))}
          </div>

          {/* Clear filters */}
          {(searchTerm || selectedCategory !== 'wszystkie') && (
            <div className="text-center mt-4">
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('wszystkie');
                }}
                className="text-sm text-charcoal/60 hover:text-charcoal transition-colors duration-300"
              >
                Wyczyść filtry
              </button>
            </div>
          )}
        </div>
      </section>

      {/* BLOG POSTS - Magazine Editorial Layout */}
      <section className="magazine-content">
        {filteredPosts.length > 0 ? (
          <div className="magazine-grid">
            {/* Featured Article - Large */}
            {filteredPosts[0] && (
              <article className="magazine-featured">
                <PostCard post={filteredPosts[0]} featured={true} className="featured-card" />
              </article>
            )}

            {/* Secondary Articles - Medium */}
            <div className="magazine-secondary">
              {filteredPosts.slice(1, 3).map((post, index) => (
                <article key={`secondary-${post?.slug || index}`} className="magazine-secondary-item">
                  <PostCard post={post} className="secondary-card" />
                </article>
              ))}
            </div>

            {/* Additional Articles - Small Grid */}
            {filteredPosts.length > 3 && (
              <div className="magazine-grid-small">
                {filteredPosts.slice(3).map((post, index) => (
                  <article key={`small-${post?.slug || index}`} className="magazine-small-item">
                    <PostCard post={post} className="small-card" />
                  </article>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="magazine-empty">
            <div className="magazine-empty-content">
              <h3 className="magazine-empty-title">
                {searchTerm || selectedCategory !== 'wszystkie'
                  ? 'Nie znaleziono artykułów'
                  : 'Wkrótce więcej treści'
                }
              </h3>
              <p className="magazine-empty-text">
                {searchTerm || selectedCategory !== 'wszystkie'
                  ? 'Spróbuj innych słów kluczowych lub zmień kategorię'
                  : 'Pracujemy nad nowymi inspirującymi artykułami'
                }
              </p>
              {(searchTerm || selectedCategory !== 'wszystkie') && (
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('wszystkie');
                  }}
                  className="mt-4 px-6 py-2 bg-enterprise-brown text-white hover:bg-enterprise-brown/90 transition-all duration-300"
                >
                  Pokaż wszystkie artykuły
                </button>
              )}
            </div>
          </div>
        )}
      </section>

      {/* COMMUNITY SECTION - BAKASANA Standards */}
      <section className="container">
        <div className="text-center space-y-lg max-w-3xl mx-auto">
          <div className="space-y-md">
            <h3 className="section-header">
              Bądź na bieżąco
            </h3>

            <p className="body-text opacity-80">
              Otrzymuj najnowsze artykuły i inspiracje z duchowych podróży
            </p>
          </div>

          {/* SACRED DIVIDER */}
          <div className="flex items-center justify-center my-12">
            <div className="flex items-center gap-sm text-charcoal-gold/60">
              <div className="w-12 h-px bg-charcoal-gold/30"></div>
              <span className="text-lg opacity-60">ॐ</span>
              <div className="w-12 h-px bg-charcoal-gold/30"></div>
            </div>
          </div>

          {/* CONTACT LINKS - Ghost buttons */}
          <div className="flex flex-col sm:flex-row gap-lg justify-center items-center">
            <a
              href="https://www.instagram.com/fly_with_bakasana"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-ghost"
            >
              Instagram
            </a>
            <a
              href="mailto:<EMAIL>"
              className="btn-ghost"
            >
              Email
            </a>
          </div>

          <div className="pt-8">
            <p className="text-sm text-stone font-light italic tracking-wide">
              "Każda historia ma swoją moc..."
            </p>
            <p className="text-xs text-charcoal-gold font-light tracking-wide uppercase mt-2">
              Om Swastiastu
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}