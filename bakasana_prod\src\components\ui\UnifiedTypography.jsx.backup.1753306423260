'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * UnifiedTypography - Ujednolicony system typografii BAKASANA
 * Elegancja Old Money + Ciepły minimalizm + Organiczne elementy
 */

// Heading Components
export function HeroTitle({ children, className = '', ...props }) {
  // Check if className contains font-size related classes to avoid conflicts
  // More specific check for font-size classes (text-xs, text-sm, text-[...], etc.)
  const hasCustomFontSize = /text-(?:xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|8xl|9xl|\[)/.test(className) ||
                           className.includes('magazine-title') ||
                           className.includes('hero-title');

  const defaultStyles = hasCustomFontSize ? {
    letterSpacing: 'clamp(0.1em, 0.5vw, 0.2em)'
  } : {
    fontSize: 'clamp(3rem, 8vw + 1rem, 10rem)',
    letterSpacing: 'clamp(0.1em, 0.5vw, 0.2em)'
  };

  return (
    <h1
      className={cn(
        "font-cormorant font-light text-charcoal leading-[0.95]",
        "text-center mb-6",
        className
      )}
      style={defaultStyles}
      {...props}
    >
      {children}
    </h1>
  );
}

export function SectionTitle({ children, level = 2, className = '', ...props }) {
  const Tag = `h${level}`;

  // Check if className contains font-size related classes to avoid conflicts
  // More specific check for font-size classes (text-xs, text-sm, text-[...], etc.)
  const hasCustomFontSize = /text-(?:xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|8xl|9xl|\[)/.test(className) ||
                           className.includes('section-title') ||
                           className.includes('hero-');

  const defaultStyles = hasCustomFontSize ? {
    letterSpacing: 'clamp(0.05em, 0.3vw, 0.1em)'
  } : {
    fontSize: 'clamp(2rem, 4vw + 0.5rem, 3.5rem)',
    letterSpacing: 'clamp(0.05em, 0.3vw, 0.1em)'
  };

  return (
    <Tag
      className={cn(
        "font-cormorant font-light text-charcoal leading-tight",
        "mb-8 text-center",
        className
      )}
      style={defaultStyles}
      {...props}
    >
      {children}
    </Tag>
  );
}

export function CardTitle({ children, level = 3, className = '', ...props }) {
  const Tag = `h${level}`;
  
  return (
    <Tag 
      className={cn(
        "font-cormorant font-light text-charcoal leading-tight",
        "text-2xl tracking-[0.05em]",
        "mb-4",
        className
      )}
      {...props}
    >
      {children}
    </Tag>
  );
}

export function SubTitle({ children, className = '', ...props }) {
  return (
    <h4 
      className={cn(
        "font-cormorant font-light text-enterprise-brown italic",
        "text-xl tracking-[0.02em] leading-relaxed",
        "mb-6 text-center opacity-90",
        className
      )}
      {...props}
    >
      {children}
    </h4>
  );
}

// Body Text Components
export function BodyText({ children, size = 'md', className = '', ...props }) {
  const sizeClasses = {
    sm: "text-sm leading-relaxed",
    md: "text-base leading-relaxed", 
    lg: "text-lg leading-relaxed"
  };
  
  return (
    <p 
      className={cn(
        "font-inter font-light text-charcoal-light",
        sizeClasses[size],
        "mb-6",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
}

export function LeadText({ children, className = '', ...props }) {
  // Check if className contains font-size related classes to avoid conflicts
  // More specific check for font-size classes (text-xs, text-sm, text-[...], etc.)
  const hasCustomFontSize = /text-(?:xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|8xl|9xl|\[)/.test(className) ||
                           className.includes('lead-') ||
                           className.includes('hero-');

  const defaultStyles = hasCustomFontSize ? {} : {
    fontSize: 'clamp(1.125rem, 2vw + 0.5rem, 1.5rem)'
  };

  return (
    <p
      className={cn(
        "font-inter font-light text-charcoal leading-relaxed",
        "mb-8 max-w-3xl mx-auto text-center",
        className
      )}
      style={defaultStyles}
      {...props}
    >
      {children}
    </p>
  );
}

export function SmallText({ children, className = '', ...props }) {
  return (
    <p 
      className={cn(
        "font-inter font-light text-sage",
        "text-sm leading-relaxed",
        "mb-4",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
}

// Special Text Components
export function Quote({ children, author, className = '', ...props }) {
  return (
    <blockquote 
      className={cn(
        "font-cormorant italic text-enterprise-brown",
        "text-2xl leading-relaxed text-center",
        "mb-8 max-w-2xl mx-auto",
        "relative",
        className
      )}
      {...props}
    >
      <span className="text-4xl opacity-30 absolute -top-4 -left-4 /* TODO: Replace with HeroTitle */">"</span>
      {children}
      <span className="text-4xl opacity-30 absolute -bottom-8 -right-4">"</span>
      {author && (
        <cite className="block font-inter font-light text-sage text-sm mt-sm not-italic">
          — {author}
        </cite>
      )}
    </blockquote>
  );
}

export function Badge({ children, variant = 'default', className = '', ...props }) {
  const variants = {
    default: "bg-enterprise-brown text-sanctuary",
    outline: "border border-enterprise-brown text-enterprise-brown bg-transparent",
    warm: "bg-terra text-sanctuary",
    minimal: "bg-whisper text-charcoal"
  };
  
  return (
    <span 
      className={cn(
        "inline-block px-3 py-1 text-xs font-inter font-medium",
        "uppercase tracking-[2px] transition-all duration-300",
        variants[variant],
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
}

// Navigation Text
export function NavLink({ children, active = false, className = '', ...props }) {
  return (
    <span 
      className={cn(
        "font-inter font-light text-xs uppercase tracking-[1.2px]",
        "transition-all duration-300",
        active 
          ? "text-enterprise-brown opacity-100" 
          : "text-charcoal-light opacity-70 hover:text-enterprise-brown hover:opacity-100",
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
}

// Form Labels
export function FormLabel({ children, required = false, className = '', ...props }) {
  return (
    <label 
      className={cn(
        "block text-sm font-inter font-light text-charcoal",
        "mb-2 tracking-wide",
        className
      )}
      {...props}
    >
      {children}
      {required && (
        <span className="text-terra ml-1" aria-label="wymagane">*</span>
      )}
    </label>
  );
}

// Stats/Numbers
export function StatNumber({ children, className = '', ...props }) {
  return (
    <div 
      className={cn(
        "font-cormorant font-extralight text-enterprise-brown",
        "text-5xl leading-none mb-2",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

export function StatLabel({ children, className = '', ...props }) {
  return (
    <div 
      className={cn(
        "font-inter font-medium text-sage",
        "text-xs uppercase tracking-[2px]",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

// Utility Components
export function Divider({ className = '', ...props }) {
  return (
    <div 
      className={cn(
        "flex items-center justify-center my-12",
        className
      )}
      {...props}
    >
      <div className="flex-1 h-px bg-stone-light max-w-20"></div>
      <div className="w-1.5 h-1.5 bg-enterprise-brown transform rotate-45 mx-6"></div>
      <div className="flex-1 h-px bg-stone-light max-w-20"></div>
    </div>
  );
}

export function OrganicAccent({ className = '', ...props }) {
  return (
    <div 
      className={cn(
        "w-12 h-0.5 bg-gradient-to-r from-transparent via-enterprise-brown to-transparent",
        "mx-auto mb-8 opacity-60",
        className
      )}
      {...props}
    />
  );
}