'use client';

import React from 'react';
import Link from 'next/link';

const BaliNotFound = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 flex items-center justify-center px-container-sm texture-bali">
      <div className="max-w-2xl mx-auto text-center relative">
        {/* Dekoracyjne elementy */}
        <div className="absolute -top-10 -left-10 text-6xl text-amber-600/10 lotus-pulse /* TODO: Replace with HeroTitle */">🐒</div>
        <div className="absolute -top-5 -right-5 text-4xl text-amber-600/15 gentle-float /* TODO: Replace with HeroTitle */">🌺</div>
        <div className="absolute -bottom-10 -right-10 text-5xl text-amber-600/10 breathing-glow /* TODO: Replace with HeroTitle */">🪷</div>
        
        {/* Główna zawartość */}
        <div className="relative z-10">
          {/* 404 z balijskim stylem */}
          <div className="mb-lg">
            <h1 className="text-8xl font-light text-amber-600/80 mb-sm font-playfair" /* TODO: Replace with HeroTitle */>
              404
            </h1>
            <div className="flex justify-center items-center gap-sm mb-md">
              <span className="text-3xl animate-pulse /* TODO: Replace with SectionTitle */">🌴</span>
              <div className="h-px bg-amber-300/30 flex-1 max-w-20"></div>
              <span className="text-2xl lotus-pulse /* TODO: Replace with SectionTitle */">🪷</span>
              <div className="h-px bg-amber-300/30 flex-1 max-w-20"></div>
              <span className="text-3xl animate-pulse">🌴</span>
            </div>
          </div>
          
          {/* Humorystyczny tytuł */}
          <h2 className="text-3xl md:text-4xl font-light text-gray-800 mb-md font-playfair" /* TODO: Replace with SectionTitle */>
            Zgubiłeś się jak w dżungli Ubud? 🐒
          </h2>
          
          {/* Opis z balijskim humorem */}
          <div className="space-y-sm mb-lg">
            <p className="text-lg text-gray-600 leading-relaxed">
              Nie martw się! Nawet najlepsi przewodnicy czasem gubią szlak w balijskiej dżungli.
            </p>
            
            <p className="text-gray-600">
              Ta strona prawdopodobnie medytuje gdzieś w świątyni lub surfuje na falach Canggu.
            </p>
          </div>
          
          {/* Balijskie pozdrowienie */}
          <div className="mb-lg p-6 bg-white/40 border border-amber-200/30">
            <p className="text-amber-700 italic text-lg mb-2 font-playfair">
              "Tidak apa-apa"
            </p>
            <p className="text-amber-600/80 text-sm">
              (To znaczy "Nie ma problemu" po indonezyjsku)
            </p>
          </div>
          
          {/* Opcje nawigacji */}
          <div className="space-y-sm">
            <p className="text-gray-600 mb-md">
              Wybierz swoją ścieżkę powrotu:
            </p>
            
            <div className="flex flex-col sm:flex-row gap-sm justify-center">
              <Link
                href="/"
                className="inline-flex items-center justify-center px-hero-padding py-3 bg-transparent text-gray-800 border-2 border-amber-400 text-sm font-light tracking-wider uppercase transition-all duration-300 hover:bg-amber-50 hover:border-amber-500 cursor-lotus"
              >
                <span className="mr-2">🏠</span>
                Powrót do domu
              </Link>
              
              <Link
                href="/program"
                className="inline-flex items-center justify-center px-hero-padding py-3 bg-amber-400 text-white border-2 border-amber-400 text-sm font-light tracking-wider uppercase transition-all duration-300 hover:bg-amber-500 hover:border-amber-500 cursor-lotus"
              >
                <span className="mr-2">🧘‍♀️</span>
                Zobacz retreaty
              </Link>
            </div>
          </div>
          
          {/* Mądre słowa */}
          <div className="mt-xl p-6 bg-gradient-to-r from-amber-50/50 to-orange-50/50 border-l-4 border-amber-300/40">
            <p className="text-gray-700 italic leading-relaxed font-playfair">
              "Czasami trzeba się zgubić, żeby odnaleźć prawdziwą drogę"
            </p>
            <div className="text-right mt-2">
              <span className="text-amber-700 text-sm">~ Balijska mądrość</span>
            </div>
          </div>
        </div>
        
        {/* Animowane małpki */}
        <div className="absolute bottom-0 left-0 text-2xl text-amber-600/20 gentle-float" style={{ animationDelay: '1s' }}>
          🐒
        </div>
        <div className="absolute bottom-10 right-0 text-2xl text-amber-600/20 gentle-float" style={{ animationDelay: '2s' }}>
          🐒
        </div>
      </div>
    </div>
  );
};

export default BaliNotFound;
