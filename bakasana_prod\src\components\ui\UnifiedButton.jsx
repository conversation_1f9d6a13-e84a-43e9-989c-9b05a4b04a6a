'use client';

import React from 'react';
import { cn  } from '@/lib/utils';

/**
 * UnifiedButton - Ujednolicony system przycisków BAKASANA
 * Elegancja Old Money + Ciepły minimalizm
 */

const buttonVariants = {
  // PRIMARY - <PERSON><PERSON><PERSON> ak<PERSON>je (CTA)
  primary: {
    base: "bg-enterprise-brown text-sanctuary border border-enterprise-brown relative overflow-hidden",
    hover: "hover:bg-terra hover:border-terra hover:shadow-[0_8px_25px_rgba(193,155,104,0.25)] hover:scale-[1.02] hover:-translate-y-0.5",
    focus: "focus:ring-2 focus:ring-enterprise-brown/20 focus:ring-offset-2"
  },
  
  // SECONDARY - Drugie w hierarchii
  secondary: {
    base: "bg-transparent text-enterprise-brown border border-enterprise-brown relative overflow-hidden",
    hover: "hover:bg-enterprise-brown hover:text-sanctuary hover:shadow-[0_6px_20px_rgba(193,155,104,0.2)] hover:scale-[1.01] hover:-translate-y-0.5",
    focus: "focus:ring-2 focus:ring-enterprise-brown/20 focus:ring-offset-2"
  },
  
  // GHOST - Subtelne akcje
  ghost: {
    base: "bg-transparent text-charcoal border-0",
    hover: "hover:bg-whisper hover:text-enterprise-brown",
    focus: "focus:ring-2 focus:ring-enterprise-brown/10 focus:ring-offset-1"
  },
  
  // MINIMAL - Ultra-subtelne
  minimal: {
    base: "bg-transparent text-sage border-0 underline decoration-1 underline-offset-4",
    hover: "hover:text-enterprise-brown hover:decoration-enterprise-brown",
    focus: "focus:ring-2 focus:ring-enterprise-brown/10 focus:ring-offset-1"
  }
};

const sizeVariants = {
  // All sizes meet WCAG 2.1 AA touch target requirements (44px minimum)
  sm: "px-6 py-3 text-xs tracking-[1px] min-h-[44px]",        // 44px height
  md: "px-8 py-3.5 text-sm tracking-[1.2px] min-h-[48px]",   // 48px height
  lg: "px-12 py-4 text-sm tracking-[1.5px] min-h-[52px]",    // 52px height
  xl: "px-16 py-5 text-base tracking-[2px] min-h-[56px]"     // 56px height
};

export default function UnifiedButton({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  disabled = false,
  loading = false,
  as: Component = 'button',
  ...props
}) {
  const variantStyles = buttonVariants[variant];
  const sizeStyles = sizeVariants[size];
  
  // Filter out button-specific props when using as a different component
  const isButton = Component === 'button';
  const componentProps = isButton 
    ? { disabled: disabled || loading, ...props }
    : props; // Don't pass disabled to non-button components
  
  return (
    <Component
      className={cn(
        // Base styles - Old Money elegance
        "inline-flex items-center justify-center font-inter font-light uppercase group",
        "transition-all duration-300 ease-out",
        "focus:outline-none",
        isButton && "disabled:opacity-50 disabled:cursor-not-allowed",
        !isButton && (disabled || loading) && "opacity-50 cursor-not-allowed pointer-events-none",
        "transform active:translate-y-0",
        
        // Variant styles
        variantStyles.base,
        variantStyles.hover,
        variantStyles.focus,
        
        // Size styles
        sizeStyles,
        
        // Loading state
        loading && "opacity-70 cursor-wait",
        
        // Custom className
        className
      )}
      {...componentProps}
    >
      {/* Subtelny shimmer effect dla primary i secondary */}
      {(variant === 'primary' || variant === 'secondary') && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
      )}
      
      {loading && (
        <svg 
          className="animate-spin -ml-1 mr-2 h-4 w-4" 
          fill="none" 
          viewBox="0 0 24 24"
        >
          <circle 
            className="opacity-25" 
            cx="12" 
            cy="12" 
            r="10" 
            stroke="currentColor" 
            strokeWidth="2"
          />
          <path 
            className="opacity-75" 
            fill="currentColor" 
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      <span className="relative z-10">{children}</span>
    </Component>
  );
}

// Wyspecjalizowane warianty dla częstych przypadków użycia

export function CTAButton({ children, ...props }) {
  return (
    <UnifiedButton variant="primary" size="lg" {...props}>
      {children}
    </UnifiedButton>
  );
}

export function SecondaryButton({ children, ...props }) {
  return (
    <UnifiedButton variant="secondary" size="md" {...props}>
      {children}
    </UnifiedButton>
  );
}

export function GhostButton({ children, ...props }) {
  return (
    <UnifiedButton variant="ghost" size="md" {...props}>
      {children}
    </UnifiedButton>
  );
}

export function LinkButton({ children, ...props }) {
  return (
    <UnifiedButton variant="minimal" size="sm" {...props}>
      {children}
    </UnifiedButton>
  );
}