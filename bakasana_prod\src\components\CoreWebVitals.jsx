'use client';

import { useEffect  } from 'react';

export default function CoreWebVitals() {
  useEffect(() => {
    // Critical performance optimizations
    if (typeof window !== 'undefined') {
      // Optimize images loading
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        if (!img.loading) {
          img.loading = 'lazy';
        }
      });

      // Optimize third-party scripts
      const scripts = document.querySelectorAll('script[src]');
      scripts.forEach(script => {
        if (script.src.includes('googletagmanager') || 
            script.src.includes('google-analytics') ||
            script.src.includes('hotjar') ||
            script.src.includes('clarity')) {
          script.async = true;
          script.defer = true;
        }
      });

      // Enhanced preloading for 100/100 performance
      const preloadResources = [
        { href: '/images/background/bali-hero.webp', as: 'image', type: 'image/webp' },
        { href: '/images/logo/bakasana-logo.svg', as: 'image', type: 'image/svg+xml' },
        { href: '/fonts/cormorant-garamond.woff2', as: 'font', type: 'font/woff2', crossorigin: 'anonymous' }
      ];

      preloadResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource.href;
        link.as = resource.as;
        if (resource.type) link.type = resource.type;
        if (resource.crossorigin) link.crossOrigin = resource.crossorigin;
        document.head.appendChild(link);
      });

      // Prefetch next likely pages
      const prefetchPages = ['/program', '/blog', '/galeria'];
      prefetchPages.forEach(page => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = page;
        document.head.appendChild(link);
      });

      // Font optimization handled by Next.js font system
      // Removed hardcoded font URLs to prevent 404 errors

      // Enhanced critical CSS for 100/100 performance
      const criticalCSS = document.createElement('style');
      criticalCSS.textContent = `
        /* Critical CSS for above-the-fold content - Premium optimization */
        body {
          font-display: swap;
          contain: layout style paint;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
          font-display: swap;
          color: #2A2724;
          background-color: #FDFCF8;
          margin: 0;
          padding: 0;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
        
        .hero-section {
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #FDFCF8 0%, #F9F7F3 100%);
          will-change: transform;
          contain: layout style paint;
        }
        
        .navbar {
          position: fixed;
          top: 0;
          width: 100%;
          z-index: 1000;
          background: rgba(253, 252, 248, 0.95);
          backdrop-filter: blur(10px);
          will-change: transform;
          contain: layout style paint;
        }
        
        .main-content {
          will-change: transform;
          contain: layout style paint;
        }
      `;
      document.head.appendChild(criticalCSS);

      // Advanced performance optimizations for 100/100 score

      // Optimize scroll performance with passive listeners
      let ticking = false;
      const optimizeScroll = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            // Optimize animations during scroll
            document.documentElement.style.setProperty('--scroll-optimized', 'true');
            ticking = false;
          });
          ticking = true;
        }
      };
      
      window.addEventListener('scroll', optimizeScroll, { passive: true });

      // Resource hints for better performance
      const resourceHints = [
        { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
        { rel: 'dns-prefetch', href: '//images.unsplash.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: 'anonymous' }
      ];

      resourceHints.forEach(hint => {
        const link = document.createElement('link');
        link.rel = hint.rel;
        link.href = hint.href;
        if (hint.crossorigin) link.crossOrigin = hint.crossorigin;
        document.head.appendChild(link);
      });

      // Optimize Web Vitals
      if ('web-vitals' in window) {
        import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
          getCLS(console.log);
          getFID(console.log);
          getFCP(console.log);
          getLCP(console.log);
          getTTFB(console.log);
        });
      }

      // Cleanup
      return () => {
        window.removeEventListener('scroll', optimizeScroll);
      };
    }
  }, []);

  return null;
}