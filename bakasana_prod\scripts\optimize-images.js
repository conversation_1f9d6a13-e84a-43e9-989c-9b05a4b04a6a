const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Enhanced configuration for Lighthouse >95 performance
const config = {
  inputDir: path.join(__dirname, '../public/images'),
  outputDir: path.join(__dirname, '../public/images'),
  formats: ['webp', 'avif'],
  sizes: [480, 640, 750, 828, 1080, 1200, 1920],
  quality: {
    webp: 85,
    avif: 80,
    jpeg: 85,
    png: 90
  },
  // Critical images that should be preloaded
  criticalImages: [
    'background/bali-hero.webp',
    'logo/bakasana-logo.webp'
  ]
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Find all images recursively
function findImages(dir) {
  const images = [];
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      images.push(...findImages(filePath));
    } else if (/\.(jpg|jpeg|png)$/i.test(file)) {
      images.push(filePath);
    }
  });
  
  return images;
}

const imageFiles = findImages(config.inputDir);

// Enhanced image optimization function
async function optimizeImage(file) {
  const relativePath = path.relative(config.inputDir, file);
  const dir = path.dirname(relativePath);
  const filename = path.basename(file, path.extname(file));
  const outputDir = path.join(config.outputDir, dir);
  
  // Ensure output directory exists
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  const image = sharp(file);
  const metadata = await image.metadata();
  
  console.log(`🔄 Processing: ${relativePath} (${metadata.width}x${metadata.height})`);

  // Generate responsive sizes
  for (const size of config.sizes) {
    if (metadata.width > size) {
      for (const format of config.formats) {
        const outputPath = path.join(outputDir, `${filename}-${size}.${format}`);
        
        try {
          await image
            .resize(size, null, { 
              withoutEnlargement: true,
              fastShrinkOnLoad: true 
            })
            [format]({ 
              quality: config.quality[format] || 80,
              effort: format === 'avif' ? 4 : undefined
            })
            .toFile(outputPath);
            
          const stats = fs.statSync(outputPath);
          console.log(`  ✅ ${format.toUpperCase()} ${size}w: ${Math.round(stats.size / 1024)}KB`);
        } catch (error) {
          console.error(`  ❌ Error creating ${format} ${size}w:`, error.message);
        }
      }
    }
  }

  // Generate original size in modern formats
  for (const format of config.formats) {
    const outputPath = path.join(outputDir, `${filename}.${format}`);
    
    try {
      await image
        [format]({ 
          quality: config.quality[format] || 80,
          effort: format === 'avif' ? 4 : undefined
        })
        .toFile(outputPath);
        
      const stats = fs.statSync(outputPath);
      console.log(`  ✅ ${format.toUpperCase()} original: ${Math.round(stats.size / 1024)}KB`);
    } catch (error) {
      console.error(`  ❌ Error creating ${format} original:`, error.message);
    }
  }

  // Generate placeholder (low quality, small size for lazy loading)
  const placeholderPath = path.join(outputDir, `${filename}-placeholder.webp`);
  try {
    await image
      .resize(20, null, { withoutEnlargement: true })
      .webp({ quality: 20 })
      .toFile(placeholderPath);
    console.log(`  🖼️ Placeholder generated`);
  } catch (error) {
    console.error(`  ❌ Error creating placeholder:`, error.message);
  }
}

// Process all images with progress tracking
async function processAllImages() {
  console.log(`🚀 BAKASANA Image Optimization Started`);
  console.log(`📸 Found ${imageFiles.length} images to optimize...\n`);
  
  let processed = 0;
  let totalSizeBefore = 0;
  let totalSizeAfter = 0;
  
  for (const file of imageFiles) {
    try {
      const statsBefore = fs.statSync(file);
      totalSizeBefore += statsBefore.size;
      
      await optimizeImage(file);
      processed++;
      
      // Calculate size after (approximate)
      const relativePath = path.relative(config.inputDir, file);
      const dir = path.dirname(relativePath);
      const filename = path.basename(file, path.extname(file));
      const webpPath = path.join(config.outputDir, dir, `${filename}.webp`);
      
      if (fs.existsSync(webpPath)) {
        totalSizeAfter += fs.statSync(webpPath).size;
      }
      
      console.log(`📊 Progress: ${processed}/${imageFiles.length} (${Math.round(processed/imageFiles.length*100)}%)\n`);
      
    } catch (error) {
      console.error(`❌ Error optimizing ${file}:`, error.message);
    }
  }
  
  const savings = totalSizeBefore - totalSizeAfter;
  const savingsPercent = Math.round((savings / totalSizeBefore) * 100);
  
  console.log('🎉 OPTIMIZATION COMPLETE!');
  console.log('========================');
  console.log(`📸 Images processed: ${processed}/${imageFiles.length}`);
  console.log(`💾 Size before: ${Math.round(totalSizeBefore / 1024 / 1024 * 100) / 100} MB`);
  console.log(`💾 Size after: ${Math.round(totalSizeAfter / 1024 / 1024 * 100) / 100} MB`);
  console.log(`🎯 Savings: ${Math.round(savings / 1024 / 1024 * 100) / 100} MB (${savingsPercent}%)`);
  console.log('\n✅ All images optimized for Lighthouse >95 performance!');
}

// Generate critical CSS for image preloading
function generateCriticalImageCSS() {
  console.log('\n🎨 Generating critical image CSS...');
  
  const criticalCSS = `
/* Critical Image Preloading - Generated by BAKASANA optimizer */
.hero-bg {
  background-image: url('/images/background/bali-hero.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Fallback for browsers without WebP support */
.no-webp .hero-bg {
  background-image: url('/images/background/bali-hero.jpg');
}

/* Lazy loading placeholder */
.lazy-image {
  background-color: #f0f0f0;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjRjBGMEYwIi8+CjxwYXRoIGQ9Ik0xMCA3QzguMzQzMTUgNyA3IDguMzQzMTUgNyAxMEM3IDExLjY1NjkgOC4zNDMxNSAxMyAxMCAxM0MxMS42NTY5IDEzIDEzIDExLjY1NjkgMTMgMTBDMTMgOC4zNDMxNSAxMS42NTY5IDcgMTAgN1oiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+');
  background-repeat: no-repeat;
  background-position: center;
  transition: opacity 0.3s ease;
}

.lazy-image.loaded {
  background-image: none;
}
`;

  const cssPath = path.join(__dirname, '../src/styles/critical-images.css');
  fs.writeFileSync(cssPath, criticalCSS);
  console.log(`✅ Critical image CSS saved to: ${cssPath}`);
}

// Run optimization
if (require.main === module) {
  processAllImages()
    .then(() => generateCriticalImageCSS())
    .catch(console.error);
}

module.exports = { optimizeImage, processAllImages };