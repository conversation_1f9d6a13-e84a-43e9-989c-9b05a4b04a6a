#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * BAKASANA CRITICAL CSS EXTRACTOR
 * Extracts and inlines critical CSS for Lighthouse >95 performance
 */

class CriticalCSSExtractor {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.stylesDir = path.join(this.projectRoot, 'src/styles');
    this.appDir = path.join(this.projectRoot, 'src/app');
    this.criticalCSS = '';
  }

  // Extract critical CSS from main stylesheets
  extractCriticalCSS() {
    console.log('🎨 Extracting critical CSS...');

    const criticalRules = [
      // Typography and fonts
      '@font-face',
      'html', 'body',
      '.font-primary', '.font-secondary',
      
      // Layout essentials
      '.container', '.max-w-', '.mx-auto',
      '.grid', '.flex', '.block', '.hidden',
      
      // Hero section (above the fold)
      '.hero-section', '.hero-bg', '.hero-content',
      '.hero-title', '.hero-subtitle', '.hero-buttons',
      
      // Navigation (critical for UX)
      '.navbar', '.nav-link', '.nav-button',
      '.mobile-menu', '.hamburger',
      
      // Critical utilities
      '.text-', '.bg-', '.p-', '.m-', '.w-', '.h-',
      '.absolute', '.relative', '.fixed', '.sticky',
      '.top-', '.left-', '.right-', '.bottom-',
      '.z-', '.opacity-', '.transform', '.transition',
      
      // Responsive utilities (mobile-first)
      '@media (min-width: 640px)',
      '@media (min-width: 768px)',
      '@media (min-width: 1024px)',
    ];

    // Read main CSS files
    const cssFiles = [
      path.join(this.appDir, 'globals.css'),
      path.join(this.stylesDir, 'main.css'),
      path.join(this.stylesDir, 'typography.css'),
      path.join(this.stylesDir, 'unified-system.css')
    ];

    let criticalCSS = '';

    cssFiles.forEach(cssFile => {
      if (fs.existsSync(cssFile)) {
        const content = fs.readFileSync(cssFile, 'utf8');
        
        // Extract critical rules
        criticalRules.forEach(rule => {
          const regex = new RegExp(`${rule.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[^}]*}`, 'g');
          const matches = content.match(regex);
          if (matches) {
            criticalCSS += matches.join('\n') + '\n';
          }
        });
      }
    });

    // Add essential CSS variables
    criticalCSS = `
/* BAKASANA Critical CSS - Inlined for Performance */
:root {
  --font-primary: 'Cormorant Garamond', serif;
  --font-secondary: 'Inter', sans-serif;
  --sanctuary: #FDFCF8;
  --linen: #F5F1E8;
  --temple-gold: #C9A575;
  --charcoal: #2C2C2C;
  --duration-quick: 0.2s;
  --duration-smooth: 0.3s;
  --ease-premium: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Critical layout styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-secondary);
  background-color: var(--sanctuary);
  color: var(--charcoal);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

/* Hero section critical styles */
.hero-section {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;
}

.hero-content {
  text-align: center;
  z-index: 1;
  max-width: 800px;
  padding: 2rem;
}

.hero-title {
  font-family: var(--font-primary);
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 300;
  color: var(--charcoal);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: clamp(1.1rem, 2vw, 1.25rem);
  color: var(--charcoal);
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* Navigation critical styles */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(253, 252, 248, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: all var(--duration-smooth) var(--ease-premium);
}

/* Critical utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.flex {
  display: flex;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.block {
  display: block;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

/* Responsive utilities */
@media (min-width: 768px) {
  .container {
    padding: 0 2rem;
  }
  
  .hero-content {
    padding: 3rem;
  }
}

` + criticalCSS;

    this.criticalCSS = this.minifyCSS(criticalCSS);
    console.log(`✅ Critical CSS extracted (${this.criticalCSS.length} characters)`);
  }

  // Minify CSS
  minifyCSS(css) {
    return css
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
      .replace(/\s+/g, ' ') // Collapse whitespace
      .replace(/;\s*}/g, '}') // Remove last semicolon in blocks
      .replace(/\s*{\s*/g, '{') // Clean up braces
      .replace(/\s*}\s*/g, '}')
      .replace(/\s*;\s*/g, ';') // Clean up semicolons
      .replace(/\s*,\s*/g, ',') // Clean up commas
      .replace(/\s*:\s*/g, ':') // Clean up colons
      .trim();
  }

  // Generate critical CSS component for Next.js
  generateCriticalCSSComponent() {
    console.log('⚛️ Generating Critical CSS component...');

    const componentCode = `
// BAKASANA Critical CSS Component
// Auto-generated for Lighthouse >95 performance

export default function CriticalCSS() {
  return (
    <style
      dangerouslySetInnerHTML={{
        __html: \`${this.criticalCSS}\`
      }}
    />
  );
}

// Export critical CSS string for server-side rendering
export const criticalCSSString = \`${this.criticalCSS}\`;
`;

    const componentPath = path.join(this.projectRoot, 'src/components/CriticalCSS.jsx');
    fs.writeFileSync(componentPath, componentCode);
    console.log(`✅ Critical CSS component saved to: ${componentPath}`);
  }

  // Update layout to include critical CSS
  updateLayout() {
    console.log('📝 Updating layout with critical CSS...');

    const layoutPath = path.join(this.appDir, 'layout.jsx');
    if (!fs.existsSync(layoutPath)) {
      console.error('❌ Layout file not found');
      return;
    }

    let layoutContent = fs.readFileSync(layoutPath, 'utf8');

    // Check if critical CSS is already imported
    if (!layoutContent.includes('CriticalCSS')) {
      // Add import
      const importLine = "import CriticalCSS from '@/components/CriticalCSS';\n";
      layoutContent = layoutContent.replace(
        /(import.*from.*\n)/g,
        `$1${importLine}`
      );

      // Add component to head
      const criticalCSSComponent = '        <CriticalCSS />\n';
      layoutContent = layoutContent.replace(
        /(<head>[\s\S]*?)(<\/head>)/,
        `$1${criticalCSSComponent}$2`
      );

      fs.writeFileSync(layoutPath, layoutContent);
      console.log('✅ Layout updated with critical CSS');
    } else {
      console.log('ℹ️ Critical CSS already present in layout');
    }
  }

  // Generate performance hints
  generatePerformanceHints() {
    console.log('💡 Generating performance hints...');

    const hintsCode = `
// BAKASANA Performance Hints
// Auto-generated resource hints for optimal loading

export const performanceHints = {
  // Critical resources to preload
  preload: [
    { href: '/images/background/bali-hero.webp', as: 'image', type: 'image/webp' },
    { href: '/fonts/cormorant-garamond-v16-latin-300.woff2', as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' },
    { href: '/fonts/inter-v13-latin-400.woff2', as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' }
  ],
  
  // Resources to prefetch
  prefetch: [
    { href: '/images/gallery/bali-1.webp', as: 'image' },
    { href: '/images/gallery/bali-2.webp', as: 'image' }
  ],
  
  // DNS prefetch for external resources
  dnsPrefetch: [
    '//fonts.googleapis.com',
    '//fonts.gstatic.com',
    '//images.unsplash.com',
    '//cdn.sanity.io'
  ]
};

// Component to inject performance hints
export default function PerformanceHints() {
  return (
    <>
      {/* Preload critical resources */}
      {performanceHints.preload.map((hint, index) => (
        <link
          key={\`preload-\${index}\`}
          rel="preload"
          href={hint.href}
          as={hint.as}
          type={hint.type}
          crossOrigin={hint.crossOrigin}
        />
      ))}
      
      {/* Prefetch non-critical resources */}
      {performanceHints.prefetch.map((hint, index) => (
        <link
          key={\`prefetch-\${index}\`}
          rel="prefetch"
          href={hint.href}
          as={hint.as}
        />
      ))}
      
      {/* DNS prefetch for external domains */}
      {performanceHints.dnsPrefetch.map((domain, index) => (
        <link
          key={\`dns-prefetch-\${index}\`}
          rel="dns-prefetch"
          href={domain}
        />
      ))}
    </>
  );
}
`;

    const hintsPath = path.join(this.projectRoot, 'src/components/PerformanceHints.jsx');
    fs.writeFileSync(hintsPath, hintsCode);
    console.log(`✅ Performance hints saved to: ${hintsPath}`);
  }

  // Run complete critical CSS extraction
  async run() {
    console.log('🚀 BAKASANA Critical CSS Extraction Started\n');
    
    this.extractCriticalCSS();
    this.generateCriticalCSSComponent();
    this.generatePerformanceHints();
    this.updateLayout();
    
    console.log('\n✅ Critical CSS extraction complete!');
    console.log('📊 Performance improvements:');
    console.log('   • Reduced render-blocking CSS');
    console.log('   • Faster First Contentful Paint (FCP)');
    console.log('   • Improved Largest Contentful Paint (LCP)');
    console.log('   • Better Cumulative Layout Shift (CLS)');
  }
}

// Run the critical CSS extractor
if (require.main === module) {
  const extractor = new CriticalCSSExtractor();
  extractor.run().catch(console.error);
}

module.exports = CriticalCSSExtractor;