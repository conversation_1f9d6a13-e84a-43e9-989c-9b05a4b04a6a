import { usePathname  } from 'next/navigation';
import Link from 'next/link';
import React from 'react';

export default SEOBreadcrumbs;

'use client';


const SEOBreadcrumbs = ({ customBreadcrumbs = null, className = "" }) => {
  const pathname = usePathname();

  // Mapowanie ścieżek na czytelne nazwy
  const pathMapping = {
    '': 'Strona główna',
    'retreaty': 'Retreaty Jogi',
    'retreaty-jogi-bali-2025': 'Retreaty Bali 2025',
    'joga-sri-lanka-retreat': 'Retreaty Sri Lanka',
    'zajecia-stacjonarne': 'Zajęcia Stacjonarne',
    'zajecia-online': 'Zajęcia Online',
    'rezerwacja': 'Rezerwacja',
    'kontakt': 'Kontakt',
    'o-mnie': 'O Julii',
    'julia-jak<PERSON><PERSON><PERSON>-instruktor': '<PERSON> - Instruktor',
    'blog': 'Blog',
    'galeria': 'Galeria',
    'program': 'Program Retreatów',
    'transformacyjne-podroze-azja': 'Transformacyjne Podróże Azja',
    'polityka-prywatnosci': 'Polityka Prywatności'
  };

  // Jeśli przekazano custom breadcrumbs, użyj ich
  if (customBreadcrumbs) {
    const breadcrumbs = [
      { href: '/', label: 'Strona główna' },
      ...customBreadcrumbs
    ];

    return (
      <nav 
        className={`breadcrumbs py-sm ${className}`}
        role="navigation" 
        aria-label="Breadcrumb"
      >
        <div className="max-w-7xl mx-auto px-container-sm">
          <ol className="flex items-center space-x-2 text-sm">
            {breadcrumbs.map((crumb, index) => (
              <li key={index} className="flex items-center">
                {index > 0 && (
                  <svg 
                    className="w-4 h-4 text-charcoal-light/40 mx-2" 
                    fill="currentColor" 
                    viewBox="0 0 20 20"
                  >
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                )}
                {index === breadcrumbs.length - 1 ? (
                  <span className="text-enterprise-brown font-medium" aria-current="page">
                    {crumb.label}
                  </span>
                ) : (
                  <Link 
                    href={crumb.href}
                    className="text-charcoal-light hover:text-enterprise-brown transition-colors duration-200"
                  >
                    {crumb.label}
                  </Link>
                )}
              </li>
            ))}
          </ol>
        </div>

        {/* Schema.org BreadcrumbList */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "BreadcrumbList",
              "itemListElement": breadcrumbs.map((crumb, index) => ({
                "@type": "ListItem",
                "position": index + 1,
                "name": crumb.label,
                "item": `https://flywithbakasana.pl${crumb.href === '/' ? '' : crumb.href}`
              }))
            })
          }}
        />
      </nav>
    );
  }

  // Automatyczne generowanie breadcrumbs na podstawie URL
  const pathSegments = pathname.split('/').filter(segment => segment !== '');
  
  if (pathSegments.length === 0) {
    return null; // Nie pokazuj breadcrumbs na stronie głównej
  }

  const breadcrumbs = [
    { href: '/', label: 'Strona główna' }
  ];

  let currentPath = '';
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const label = pathMapping[segment] || segment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    
    breadcrumbs.push({
      href: currentPath,
      label: label
    });
  });

  return (
    <nav 
      className={`breadcrumbs py-sm bg-whisper/30 ${className}`}
      role="navigation" 
      aria-label="Breadcrumb"
    >
      <div className="max-w-7xl mx-auto px-container-sm">
        <ol className="flex items-center space-x-2 text-sm">
          {breadcrumbs.map((crumb, index) => (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <svg 
                  className="w-4 h-4 text-charcoal-light/40 mx-2" 
                  fill="currentColor" 
                  viewBox="0 0 20 20"
                >
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              )}
              {index === breadcrumbs.length - 1 ? (
                <span className="text-enterprise-brown font-medium" aria-current="page">
                  {crumb.label}
                </span>
              ) : (
                <Link 
                  href={crumb.href}
                  className="text-charcoal-light hover:text-enterprise-brown transition-colors duration-200"
                >
                  {crumb.label}
                </Link>
              )}
            </li>
          ))}
        </ol>
      </div>

      {/* Schema.org BreadcrumbList */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": breadcrumbs.map((crumb, index) => ({
              "@type": "ListItem",
              "position": index + 1,
              "name": crumb.label,
              "item": `https://flywithbakasana.pl${crumb.href === '/' ? '' : crumb.href}`
            }))
          })
        }}
      />
    </nav>
  );
};

