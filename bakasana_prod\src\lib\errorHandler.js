/**
 * Enhanced Error Handling Utilities
 * Provides consistent error handling across the application
 */

// Custom error classes
export class ValidationError extends <PERSON>rror {
  constructor(message, field = null) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
    this.statusCode = 400;
  }
}

export class AuthenticationError extends Error {
  constructor(message = 'Authentication required') {
    super(message);
    this.name = 'AuthenticationError';
    this.statusCode = 401;
  }
}

export class AuthorizationError extends Error {
  constructor(message = 'Insufficient permissions') {
    super(message);
    this.name = 'AuthorizationError';
    this.statusCode = 403;
  }
}

export class NotFoundError extends Error {
  constructor(message = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
    this.statusCode = 404;
  }
}

export class RateLimitError extends Error {
  constructor(message = 'Rate limit exceeded') {
    super(message);
    this.name = 'RateLimitError';
    this.statusCode = 429;
  }
}

export class ExternalServiceError extends Error {
  constructor(message, service = null) {
    super(message);
    this.name = 'ExternalServiceError';
    this.service = service;
    this.statusCode = 502;
  }
}

/**
 * Input validation utilities
 */
export const validators = {
  email: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new ValidationError('Invalid email format', 'email');
    }
    return true;
  },

  phone: (phone) => {
    const phoneRegex = /^\+?[\d\s\-\(\)]{9,}$/;
    if (!phoneRegex.test(phone)) {
      throw new ValidationError('Invalid phone number format', 'phone');
    }
    return true;
  },

  required: (value, fieldName) => {
    if (!value || (typeof value === 'string' && !value.trim())) {
      throw new ValidationError(`${fieldName} is required`, fieldName);
    }
    return true;
  },

  minLength: (value, min, fieldName) => {
    if (typeof value === 'string' && value.length < min) {
      throw new ValidationError(`${fieldName} must be at least ${min} characters`, fieldName);
    }
    return true;
  },

  maxLength: (value, max, fieldName) => {
    if (typeof value === 'string' && value.length > max) {
      throw new ValidationError(`${fieldName} must not exceed ${max} characters`, fieldName);
    }
    return true;
  },

  isNumber: (value, fieldName) => {
    if (isNaN(Number(value))) {
      throw new ValidationError(`${fieldName} must be a valid number`, fieldName);
    }
    return true;
  },

  isPositive: (value, fieldName) => {
    if (Number(value) <= 0) {
      throw new ValidationError(`${fieldName} must be positive`, fieldName);
    }
    return true;
  },

  isDate: (value, fieldName) => {
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      throw new ValidationError(`${fieldName} must be a valid date`, fieldName);
    }
    return true;
  },

  isFutureDate: (value, fieldName) => {
    const date = new Date(value);
    if (date <= new Date()) {
      throw new ValidationError(`${fieldName} must be a future date`, fieldName);
    }
    return true;
  }
};

/**
 * Validate object against schema
 */
export function validateSchema(data, schema) {
  const errors = [];

  for (const [field, rules] of Object.entries(schema)) {
    const value = data[field];

    for (const rule of rules) {
      try {
        if (typeof rule === 'function') {
          rule(value, field);
        } else if (typeof rule === 'object') {
          const { validator, ...params } = rule;
          validator(value, ...Object.values(params), field);
        }
      } catch (error) {
        if (error instanceof ValidationError) {
          errors.push(error);
        } else {
          errors.push(new ValidationError(`Validation failed for ${field}`, field));
        }
      }
    }
  }

  if (errors.length > 0) {
    const error = new ValidationError('Validation failed');
    error.errors = errors;
    throw error;
  }

  return true;
}

/**
 * Async error wrapper for API routes
 */
export function asyncHandler(fn) {
  return async (req, res) => {
    try {
      await fn(req, res);
    } catch (error) {
      handleApiError(error, res);
    }
  };
}

/**
 * Handle API errors consistently
 */
export function handleApiError(error, res) {
  // Log error for debugging
  console.error('API Error:', {
    name: error.name,
    message: error.message,
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    timestamp: new Date().toISOString()
  });

  // Determine status code
  const statusCode = error.statusCode || 500;

  // Prepare error response
  const errorResponse = {
    success: false,
    error: {
      name: error.name || 'Error',
      message: error.message || 'An unexpected error occurred'
    },
    timestamp: new Date().toISOString()
  };

  // Add validation errors if present
  if (error.errors && Array.isArray(error.errors)) {
    errorResponse.error.details = error.errors.map(err => ({
      field: err.field,
      message: err.message
    }));
  }

  // Add stack trace in development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.stack = error.stack;
  }

  // Send error response
  res.status(statusCode).json(errorResponse);
}

/**
 * Client-side error handler
 */
export function handleClientError(error, context = '') {
  // Log error
  console.error(`Client Error ${context}:`, error);

  // Send to error tracking service if available
  if (typeof window !== 'undefined') {
    // Sentry
    if (window.Sentry) {
      window.Sentry.captureException(error, {
        tags: { context },
        extra: { timestamp: new Date().toISOString() }
      });
    }

    // Mixpanel
    if (window.mixpanel) {
      window.mixpanel.track('Client Error', {
        error_name: error.name,
        error_message: error.message,
        context,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Return user-friendly error message
  if (error instanceof ValidationError) {
    return {
      type: 'validation',
      message: error.message,
      field: error.field
    };
  }

  if (error instanceof AuthenticationError) {
    return {
      type: 'auth',
      message: 'Please log in to continue'
    };
  }

  if (error instanceof AuthorizationError) {
    return {
      type: 'auth',
      message: 'You do not have permission to perform this action'
    };
  }

  if (error instanceof NotFoundError) {
    return {
      type: 'not_found',
      message: 'The requested resource was not found'
    };
  }

  if (error instanceof RateLimitError) {
    return {
      type: 'rate_limit',
      message: 'Too many requests. Please try again later'
    };
  }

  if (error instanceof ExternalServiceError) {
    return {
      type: 'service',
      message: 'External service is temporarily unavailable'
    };
  }

  // Generic error
  return {
    type: 'generic',
    message: 'An unexpected error occurred. Please try again'
  };
}

/**
 * Retry mechanism for external API calls
 */
export async function withRetry(fn, maxRetries = 3, delay = 1000) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Don't retry on client errors (4xx)
      if (error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
        throw error;
      }

      if (attempt === maxRetries) {
        throw new ExternalServiceError(
          `Failed after ${maxRetries} attempts: ${error.message}`
        );
      }

      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt - 1)));
    }
  }

  throw lastError;
}

/**
 * Safe JSON parsing
 */
export function safeJsonParse(str, defaultValue = null) {
  try {
    return JSON.parse(str);
  } catch (error) {
    console.warn('JSON parse error:', error.message);
    return defaultValue;
  }
}

/**
 * Environment variable validation
 */
export function validateEnvVars(requiredVars) {
  const missing = [];

  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  }

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

const errorHandler = {
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  RateLimitError,
  ExternalServiceError,
  validators,
  validateSchema,
  asyncHandler,
  handleApiError,
  handleClientError,
  withRetry,
  safeJsonParse,
  validateEnvVars
};

export default errorHandler;
