'use client';

import React from 'react';
import { ZenLoader } from '@/components/Performance/LoadingStates';

const PageLoader = ({ 
  isLoading = false, 
  message = "Ładowanie...", 
  overlay = true,
  size = "lg" 
}) => {
  if (!isLoading) return null;

  if (overlay) {
    return (
      <div className="fixed inset-0 bg-sanctuary/80 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-2xl p-8 max-w-sm mx-4 text-center">
          <ZenLoader size={size} className="mb-4" />
          <p className="text-charcoal font-cormorant text-lg">
            {message}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center py-12">
      <ZenLoader size={size} className="mb-4" />
      <p className="text-charcoal font-cormorant text-lg">
        {message}
      </p>
    </div>
  );
};

export default PageLoader;