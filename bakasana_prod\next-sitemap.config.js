/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://bakasana-travel.blog',
  generateRobotsTxt: true,
  robotsTxtOptions: {
    additionalSitemaps: [
      `${process.env.NEXT_PUBLIC_SITE_URL || 'https://bakasana-travel.blog'}/server-sitemap.xml`,
    ],
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/admin', '/private', '/api/*']
      }
    ]
  },
  exclude: ['/admin/*', '/private/*', '/api/*', '/hero-demo', '/old-money', '/test-galeria', '/robots.txt', '/sitemap.xml'],
  generateIndexSitemap: true,
  changefreq: 'weekly',
  priority: 0.7,
  transform: async (config, path) => {
    // Strona główna - najwyższy priorytet
    if (path === '/') {
      return {
        loc: path,
        changefreq: 'daily',
        priority: 1.0,
        lastmod: new Date().toISOString()
      };
    }

    // Posty blogowe - wysoki priorytet
    if (path.includes('/blog/')) {
      return {
        loc: path,
        changefreq: 'monthly',
        priority: 0.8,
        lastmod: new Date().toISOString()
      };
    }

    // Główne strony usług - wysoki priorytet
    if (['/retreaty', '/o-mnie', '/kontakt', '/rezerwacja'].includes(path)) {
      return {
        loc: path,
        changefreq: 'weekly',
        priority: 0.9,
        lastmod: new Date().toISOString()
      };
    }

    return {
      loc: path,
      changefreq: config.changefreq,
      priority: config.priority,
      lastmod: new Date().toISOString()
    };
  }
};