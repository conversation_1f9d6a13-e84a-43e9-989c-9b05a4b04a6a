import { usePathname  } from 'next/navigation';
import { useState, useEffect  } from 'react';
import Link from 'next/link';

import { motion  } from 'framer-motion';

import UnifiedButton from '@/components/ui/UnifiedButton';

export default OldMoneyNavbar;

'use client';


interface NavItem {
  label: string;
  href: string;
  isHighlighted?: boolean;
}

const navItems: NavItem[] = [
  { label: 'RETREATY', href: '/retreaty' },
  { label: 'ZAJĘCIA ONLINE', href: '/zajecia-online', isHighlighted: true },
  { label: 'BLOG', href: '/blog' },
  { label: 'O MNIE', href: '/o-mnie' },
  { label: 'KONTAKT', href: '/kontakt' }
];

const OldMoneyNavbar: React.FC = () => {
  const [scrolled, setScrolled] = useState(false);
  const [mounted, setMounted] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    setMounted(true);
    
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isActiveLink = (href: string) => {
    if (href === '/') return pathname === '/';
    return pathname.startsWith(href);
  };

  if (!mounted) return null;

  return (
    <motion.nav
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, ease: 'easeOut' }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        scrolled 
          ? 'bg-glass-nav backdrop-blur-md shadow-subtle-shadow' 
          : 'bg-transparent'
      }`}
    >
      <div className="container mx-auto px-hero-padding">
        <div className="flex items-center justify-between h-nav-height">
          {/* Logo */}
          <Link 
            href="/" 
            className="group relative"
          >
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
              className="font-cormorant text-[22px] font-light text-charcoal tracking-[0.12em] transition-all duration-300 group-hover:text-enterprise-brown"
            >
              BAKASANA
              <div className="absolute bottom-0 left-0 w-0 h-px bg-enterprise-brown transition-all duration-300 group-hover:w-full" />
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-10">
            {navItems.map((item, index) => (
              <motion.div
                key={item.href}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
              >
                <Link
                  href={item.href}
                  className={`group relative text-small font-normal tracking-[0.05em] uppercase transition-all duration-300 ${
                    isActiveLink(item.href)
                      ? 'text-enterprise-brown'
                      : 'text-charcoal hover:text-enterprise-brown hover:-translate-y-0.5'
                  } ${
                    item.isHighlighted 
                      ? 'px-container-sm py-2 border border-enterprise-brown text-enterprise-brown hover:bg-enterprise-brown hover:text-white' 
                      : 'py-2'
                  }`}
                >
                  <span className="relative z-10">{item.label}</span>
                  
                  {/* Underline animation for regular links */}
                  {!item.isHighlighted && (
                    <div className={`absolute bottom-0 left-0 h-px bg-enterprise-brown transition-all duration-300 ${
                      isActiveLink(item.href) ? 'w-full' : 'w-0 group-hover:w-full'
                    }`} />
                  )}
                  
                  {/* Background fill for highlighted link */}
                  {item.isHighlighted && (
                    <div className="absolute inset-0 bg-enterprise-brown transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
                  )}
                </Link>
              </motion.div>
            ))}
          </div>

          {/* Mobile Menu Button */}
          <UnifiedButton variant="primary" size="md"
            aria-label="Toggle menu"
          >
            <div className="w-6 h-0.5 bg-charcoal transition-all duration-300 group-hover:bg-enterprise-brown" />
            <div className="w-6 h-0.5 bg-charcoal transition-all duration-300 group-hover:bg-enterprise-brown" />
            <div className="w-6 h-0.5 bg-charcoal transition-all duration-300 group-hover:bg-enterprise-brown" />
          </UnifiedButton>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      <div className="md:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-40 opacity-0 invisible transition-all duration-300">
        <div className="absolute right-0 top-0 h-full w-80 bg-pearl border-l border-enterprise-brown/10 p-8 transform translate-x-full transition-transform duration-300">
          <div className="flex flex-col space-y-md mt-20">
            {navItems.map((item, index) => (
              <motion.div
                key={item.href}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Link
                  href={item.href}
                  className={`block font-cormorant text-heading text-charcoal transition-all duration-300 hover:text-enterprise-brown hover:translate-x-2 ${
                    isActiveLink(item.href) ? 'text-enterprise-brown font-medium' : ''
                  }`}
                >
                  {item.label}
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </motion.nav>
  );
};

