'use client';

import { useEffect, useState  } from 'react';

// Quality Assurance monitoring component
export default function QualityAssurance() {
  const [metrics, setMetrics] = useState({
    performance: null,
    accessibility: null,
    responsiveness: null,
    animations: null
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Performance monitoring
    const checkPerformance = () => {
      if ('performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0];
        const paint = performance.getEntriesByType('paint');
        
        const performanceScore = {
          fcp: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
          lcp: 0, // Will be updated by observer
          cls: 0, // Will be updated by observer
          fid: 0, // Will be updated by observer
          ttfb: navigation?.responseStart - navigation?.requestStart || 0
        };

        setMetrics(prev => ({ ...prev, performance: performanceScore }));
      }
    };

    // Accessibility validation
    const checkAccessibility = () => {
      const issues = [];
      
      // Check for missing alt text
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        if (!img.alt && !img.getAttribute('aria-label')) {
          issues.push('Missing alt text on image');
        }
      });

      // Check for proper heading hierarchy
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      let lastLevel = 0;
      headings.forEach(heading => {
        const level = parseInt(heading.tagName.charAt(1));
        if (level > lastLevel + 1) {
          issues.push(`Heading hierarchy skip: ${heading.tagName}`);
        }
        lastLevel = level;
      });

      // Check for focus indicators
      const interactiveElements = document.querySelectorAll('button, a, input, select, textarea');
      interactiveElements.forEach(el => {
        const styles = window.getComputedStyle(el, ':focus-visible');
        if (!styles.outline && !styles.boxShadow) {
          issues.push('Missing focus indicator');
        }
      });

      setMetrics(prev => ({ 
        ...prev, 
        accessibility: { 
          score: Math.max(0, 100 - issues.length * 10),
          issues 
        }
      }));
    };

    // Responsiveness testing
    const checkResponsiveness = () => {
      const breakpoints = [320, 768, 1024, 1440, 1920];
      const issues = [];

      // Check for horizontal scroll
      if (document.body.scrollWidth > window.innerWidth) {
        issues.push('Horizontal scroll detected');
      }

      // Check for fixed widths that might break
      const elements = document.querySelectorAll('*');
      elements.forEach(el => {
        const styles = window.getComputedStyle(el);
        if (styles.width && styles.width.includes('px') && parseInt(styles.width) > window.innerWidth) {
          issues.push('Fixed width element exceeds viewport');
        }
      });

      setMetrics(prev => ({ 
        ...prev, 
        responsiveness: { 
          score: Math.max(0, 100 - issues.length * 15),
          issues 
        }
      }));
    };

    // Animation performance check
    const checkAnimations = () => {
      const issues = [];
      
      // Check for animations that might cause jank
      const animatedElements = document.querySelectorAll('[style*="transition"], [class*="animate-"]');
      animatedElements.forEach(el => {
        const styles = window.getComputedStyle(el);
        
        // Check if animating expensive properties
        if (styles.transition && (
          styles.transition.includes('width') ||
          styles.transition.includes('height') ||
          styles.transition.includes('top') ||
          styles.transition.includes('left')
        )) {
          issues.push('Animating expensive properties');
        }
      });

      // Check for reduced motion compliance
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      if (prefersReducedMotion) {
        const stillAnimating = document.querySelectorAll('[class*="animate-"]:not([class*="animate-none"])');
        if (stillAnimating.length > 0) {
          issues.push('Animations not respecting reduced motion preference');
        }
      }

      setMetrics(prev => ({ 
        ...prev, 
        animations: { 
          score: Math.max(0, 100 - issues.length * 20),
          issues 
        }
      }));
    };

    // Run checks
    setTimeout(() => {
      checkPerformance();
      checkAccessibility();
      checkResponsiveness();
      checkAnimations();
    }, 2000);

    // Set up performance observers
    if ('PerformanceObserver' in window) {
      // LCP Observer
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        setMetrics(prev => ({
          ...prev,
          performance: {
            ...prev.performance,
            lcp: lastEntry.startTime
          }
        }));
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // CLS Observer
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        setMetrics(prev => ({
          ...prev,
          performance: {
            ...prev.performance,
            cls: clsValue
          }
        }));
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });

      // FID Observer
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          setMetrics(prev => ({
            ...prev,
            performance: {
              ...prev.performance,
              fid: entry.processingStart - entry.startTime
            }
          }));
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
    }

    // Cleanup
    return () => {
      // Cleanup observers if needed
    };
  }, []);

  // Calculate overall score
  const calculateOverallScore = () => {
    if (!metrics.performance || !metrics.accessibility || !metrics.responsiveness || !metrics.animations) {
      return 0;
    }

    const performanceScore = Math.max(0, 100 - (
      (metrics.performance.fcp > 1800 ? 20 : 0) +
      (metrics.performance.lcp > 2500 ? 20 : 0) +
      (metrics.performance.cls > 0.1 ? 20 : 0) +
      (metrics.performance.fid > 100 ? 20 : 0) +
      (metrics.performance.ttfb > 600 ? 20 : 0)
    ));

    return Math.round((
      performanceScore * 0.4 +
      metrics.accessibility.score * 0.3 +
      metrics.responsiveness.score * 0.2 +
      metrics.animations.score * 0.1
    ));
  };

  const overallScore = calculateOverallScore();

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-charcoal text-silk p-4 rounded-lg shadow-lg z-50 max-w-sm">
      <h3 className="font-bold mb-2">Quality Score: {overallScore}/100</h3>
      
      {metrics.performance && (
        <div className="mb-2">
          <p className="text-sm font-medium">Performance:</p>
          <p className="text-xs">FCP: {Math.round(metrics.performance.fcp)}ms</p>
          <p className="text-xs">LCP: {Math.round(metrics.performance.lcp)}ms</p>
          <p className="text-xs">CLS: {metrics.performance.cls.toFixed(3)}</p>
        </div>
      )}

      {metrics.accessibility && (
        <div className="mb-2">
          <p className="text-sm font-medium">Accessibility: {metrics.accessibility.score}/100</p>
          {metrics.accessibility.issues.length > 0 && (
            <p className="text-xs text-sunset">{metrics.accessibility.issues.length} issues found</p>
          )}
        </div>
      )}

      {metrics.responsiveness && (
        <div className="mb-2">
          <p className="text-sm font-medium">Responsive: {metrics.responsiveness.score}/100</p>
        </div>
      )}

      {metrics.animations && (
        <div>
          <p className="text-sm font-medium">Animations: {metrics.animations.score}/100</p>
        </div>
      )}
    </div>
  );
}
