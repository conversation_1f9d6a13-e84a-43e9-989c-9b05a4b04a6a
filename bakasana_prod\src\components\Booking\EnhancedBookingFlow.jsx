'use client';

import React, { useState, useEffect } from 'react';
import { UserIcon, 
  CreditCardIcon, 
  CheckCircleIcon, 
  CalendarDaysIcon,
  MapPinIcon,
  UsersIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon
 } from 'import { Icon  } from '@/components/ui/IconSystem';
@heroicons/react/24/outline';

import UnifiedButton from '@/components/ui/UnifiedButton';
const EnhancedBookingFlow = ({ 
  retreat, 
  onComplete, 
  className = '' 
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    nationality: '',
    passportNumber: '',
    
    // Accommodation
    accommodationType: 'shared',
    roommateName: '',
    specialRequests: '',
    
    // Dietary & Health
    dietaryRestrictions: '',
    allergies: '',
    medicalConditions: '',
    yogaExperience: 'beginner',
    
    // Travel
    arrivalDate: '',
    departureDate: '',
    flightDetails: '',
    
    // Emergency Contact
    emergencyName: '',
    emergencyPhone: '',
    emergencyRelation: '',
    
    // Payment
    paymentMethod: 'full',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    billingAddress: '',
    
    // Legal
    termsAccepted: false,
    privacyAccepted: false,
    travelInsurance: false
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const steps = [
    {
      id: 1,
      title: 'Personal Info',
      icon: UserIcon,
      description: 'Tell us about yourself'
    },
    {
      id: 2,
      title: 'Accommodation',
      icon: MapPinIcon,
      description: 'Choose your room type'
    },
    {
      id: 3,
      title: 'Health & Dietary',
      icon: DocumentTextIcon,
      description: 'Special requirements'
    },
    {
      id: 4,
      title: 'Travel Details',
      icon: CalendarDaysIcon,
      description: 'Arrival and departure'
    },
    {
      id: 5,
      title: 'Payment',
      icon: CreditCardIcon,
      description: 'Secure payment'
    },
    {
      id: 6,
      title: 'Confirmation',
      icon: CheckCircleIcon,
      description: 'All done!'
    }
  ];

  const accommodationOptions = [
    {
      id: 'shared',
      name: 'Shared Room',
      description: 'Twin beds, shared bathroom',
      price: 0,
      popular: true
    },
    {
      id: 'private',
      name: 'Private Room',
      description: 'Double bed, private bathroom',
      price: 300,
      popular: false
    },
    {
      id: 'suite',
      name: 'Suite',
      description: 'King bed, private bathroom, terrace',
      price: 600,
      popular: false
    }
  ];

  const yogaExperienceOptions = [
    { id: 'beginner', name: 'Beginner', description: 'New to yoga' },
    { id: 'intermediate', name: 'Intermediate', description: '1-3 years practice' },
    { id: 'advanced', name: 'Advanced', description: '3+ years practice' }
  ];

  const paymentOptions = [
    {
      id: 'full',
      name: 'Full Payment',
      description: 'Pay the full amount now',
      discount: 100
    },
    {
      id: 'deposit',
      name: 'Deposit',
      description: 'Pay €500 now, rest later',
      discount: 0
    }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateStep = (step) => {
    const newErrors = {};
    
    switch (step) {
      case 1:
        if (!formData.firstName) newErrors.firstName = 'First name is required';
        if (!formData.lastName) newErrors.lastName = 'Last name is required';
        if (!formData.email) newErrors.email = 'Email is required';
        if (!formData.phone) newErrors.phone = 'Phone number is required';
        if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required';
        if (!formData.nationality) newErrors.nationality = 'Nationality is required';
        break;
      
      case 2:
        if (!formData.accommodationType) newErrors.accommodationType = 'Please select accommodation type';
        if (formData.accommodationType === 'shared' && !formData.roommateName) {
          newErrors.roommateName = 'Roommate name is required for shared rooms';
        }
        break;
      
      case 3:
        if (!formData.yogaExperience) newErrors.yogaExperience = 'Please select your yoga experience';
        break;
      
      case 4:
        if (!formData.arrivalDate) newErrors.arrivalDate = 'Arrival date is required';
        if (!formData.departureDate) newErrors.departureDate = 'Departure date is required';
        if (!formData.emergencyName) newErrors.emergencyName = 'Emergency contact name is required';
        if (!formData.emergencyPhone) newErrors.emergencyPhone = 'Emergency contact phone is required';
        break;
      
      case 5:
        if (!formData.paymentMethod) newErrors.paymentMethod = 'Please select payment method';
        if (!formData.cardNumber) newErrors.cardNumber = 'Card number is required';
        if (!formData.expiryDate) newErrors.expiryDate = 'Expiry date is required';
        if (!formData.cvv) newErrors.cvv = 'CVV is required';
        if (!formData.termsAccepted) newErrors.termsAccepted = 'You must accept terms and conditions';
        if (!formData.privacyAccepted) newErrors.privacyAccepted = 'You must accept privacy policy';
        break;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length));
    }
  };

  const handlePreviousStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(5)) return;
    
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setCurrentStep(6);
      
      // Call completion callback
      if (onComplete) {
        onComplete({
          ...formData,
          retreat,
          bookingId: `BK-${Date.now()}`,
          totalAmount: calculateTotal()
        });
      }
    } catch (error) {
      console.error('Booking error:', error);
      setErrors({ submit: 'Booking failed. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateTotal = () => {
    const basePrice = retreat?.price || 2800;
    const accommodationPrice = accommodationOptions.find(
      opt => opt.id === formData.accommodationType
    )?.price || 0;
    
    const subtotal = basePrice + accommodationPrice;
    const discount = formData.paymentMethod === 'full' ? 100 : 0;
    
    return subtotal - discount;
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-sm">
              <div>
                <label className="block text-sm font-medium text-charcoal mb-2">
                  First Name *
                </label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                    errors.firstName ? 'border-charcoal-gold' : 'border-stone-light'
                  }`}
                  placeholder="Enter your first name"
                />
                {errors.firstName && (
                  <p className="mt-1 text-sm text-charcoal-gold">{errors.firstName}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-charcoal mb-2">
                  Last Name *
                </label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                    errors.lastName ? 'border-charcoal-gold' : 'border-stone-light'
                  }`}
                  placeholder="Enter your last name"
                />
                {errors.lastName && (
                  <p className="mt-1 text-sm text-charcoal-gold">{errors.lastName}</p>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-sm">
              <div>
                <label className="block text-sm font-medium text-charcoal mb-2">
                  Email *
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                    errors.email ? 'border-charcoal-gold' : 'border-stone-light'
                  }`}
                  placeholder="Enter your email"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-charcoal-gold">{errors.email}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-charcoal mb-2">
                  Phone *
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                    errors.phone ? 'border-charcoal-gold' : 'border-stone-light'
                  }`}
                  placeholder="Enter your phone number"
                />
                {errors.phone && (
                  <p className="mt-1 text-sm text-charcoal-gold">{errors.phone}</p>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-sm">
              <div>
                <label className="block text-sm font-medium text-charcoal mb-2">
                  Date of Birth *
                </label>
                <input
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                    errors.dateOfBirth ? 'border-charcoal-gold' : 'border-stone-light'
                  }`}
                />
                {errors.dateOfBirth && (
                  <p className="mt-1 text-sm text-charcoal-gold">{errors.dateOfBirth}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-charcoal mb-2">
                  Nationality *
                </label>
                <select
                  value={formData.nationality}
                  onChange={(e) => handleInputChange('nationality', e.target.value)}
                  className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                    errors.nationality ? 'border-charcoal-gold' : 'border-stone-light'
                  }`}
                >
                  <option value="">Select nationality</option>
                  <option value="polish">Polish</option>
                  <option value="german">German</option>
                  <option value="other">Other</option>
                </select>
                {errors.nationality && (
                  <p className="mt-1 text-sm text-charcoal-gold">{errors.nationality}</p>
                )}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-charcoal mb-2">
                Passport Number
              </label>
              <input
                type="text"
                value={formData.passportNumber}
                onChange={(e) => handleInputChange('passportNumber', e.target.value)}
                className="w-full px-3 py-2 border border-stone-light  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold"
                placeholder="Enter passport number"
              />
              <p className="mt-1 text-xs text-stone">
                Required for international travel arrangements
              </p>
            </div>
          </div>
        );
      
      case 2:
        return (
          <div className="space-y-md">
            <div>
              <h3 className="text-lg font-cormorant font-medium text-charcoal mb-sm /* TODO: Replace with CardTitle */">
                Choose Your Accommodation
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-sm">
                {accommodationOptions.map((option) => (
                  <div
                    key={option.id}
                    className={`relative border-2 p-4 cursor-pointer transition-all ${
                      formData.accommodationType === option.id
                        ? 'border-charcoal-gold bg-charcoal-gold/5'
                        : 'border-stone-light hover:border-charcoal-gold/50'
                    }`}
                    onClick={() => handleInputChange('accommodationType', option.id)}
                  >
                    {option.popular && (
                      <div className="absolute top-0 right-0 bg-charcoal-gold text-white px-2 py-1 text-xs">
                        Popular
                      </div>
                    )}
                    
                    <h4 className="font-medium text-charcoal mb-2">{option.name}</h4>
                    <p className="text-sm text-stone mb-3">{option.description}</p>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-medium text-charcoal">
                        {option.price === 0 ? 'Included' : `+€${option.price}`}
                      </span>
                      {formData.accommodationType === option.id && (
                        <CheckCircleIcon className="h-5 w-5 text-charcoal-gold" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
              {errors.accommodationType && (
                <p className="mt-2 text-sm text-charcoal-gold">{errors.accommodationType}</p>
              )}
            </div>
            
            {formData.accommodationType === 'shared' && (
              <div>
                <label className="block text-sm font-medium text-charcoal mb-2">
                  Roommate Name *
                </label>
                <input
                  type="text"
                  value={formData.roommateName}
                  onChange={(e) => handleInputChange('roommateName', e.target.value)}
                  className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                    errors.roommateName ? 'border-charcoal-gold' : 'border-stone-light'
                  }`}
                  placeholder="Enter your roommate's name"
                />
                {errors.roommateName && (
                  <p className="mt-1 text-sm text-charcoal-gold">{errors.roommateName}</p>
                )}
              </div>
            )}
            
            <div>
              <label className="block text-sm font-medium text-charcoal mb-2">
                Special Requests
              </label>
              <textarea
                value={formData.specialRequests}
                onChange={(e) => handleInputChange('specialRequests', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-stone-light  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold"
                placeholder="Any special accommodation requests..."
              />
            </div>
          </div>
        );
      
      case 3:
        return (
          <div className="space-y-md">
            <div>
              <h3 className="text-lg font-cormorant font-medium text-charcoal mb-sm">
                Yoga Experience
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-sm">
                {yogaExperienceOptions.map((option) => (
                  <div
                    key={option.id}
                    className={`border-2 p-4 cursor-pointer transition-all ${
                      formData.yogaExperience === option.id
                        ? 'border-charcoal-gold bg-charcoal-gold/5'
                        : 'border-stone-light hover:border-charcoal-gold/50'
                    }`}
                    onClick={() => handleInputChange('yogaExperience', option.id)}
                  >
                    <h4 className="font-medium text-charcoal mb-2">{option.name}</h4>
                    <p className="text-sm text-stone">{option.description}</p>
                    
                    {formData.yogaExperience === option.id && (
                      <CheckCircleIcon className="h-5 w-5 text-charcoal-gold mt-2" />
                    )}
                  </div>
                ))}
              </div>
              {errors.yogaExperience && (
                <p className="mt-2 text-sm text-charcoal-gold">{errors.yogaExperience}</p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-charcoal mb-2">
                Dietary Restrictions
              </label>
              <textarea
                value={formData.dietaryRestrictions}
                onChange={(e) => handleInputChange('dietaryRestrictions', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-stone-light  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold"
                placeholder="Please describe any dietary restrictions or preferences..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-charcoal mb-2">
                Allergies
              </label>
              <textarea
                value={formData.allergies}
                onChange={(e) => handleInputChange('allergies', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-stone-light  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold"
                placeholder="List any allergies we should be aware of..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-charcoal mb-2">
                Medical Conditions
              </label>
              <textarea
                value={formData.medicalConditions}
                onChange={(e) => handleInputChange('medicalConditions', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-stone-light  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold"
                placeholder="Any medical conditions or physical limitations..."
              />
            </div>
          </div>
        );
      
      case 4:
        return (
          <div className="space-y-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-sm">
              <div>
                <label className="block text-sm font-medium text-charcoal mb-2">
                  Arrival Date *
                </label>
                <input
                  type="date"
                  value={formData.arrivalDate}
                  onChange={(e) => handleInputChange('arrivalDate', e.target.value)}
                  className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                    errors.arrivalDate ? 'border-charcoal-gold' : 'border-stone-light'
                  }`}
                />
                {errors.arrivalDate && (
                  <p className="mt-1 text-sm text-charcoal-gold">{errors.arrivalDate}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-charcoal mb-2">
                  Departure Date *
                </label>
                <input
                  type="date"
                  value={formData.departureDate}
                  onChange={(e) => handleInputChange('departureDate', e.target.value)}
                  className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                    errors.departureDate ? 'border-charcoal-gold' : 'border-stone-light'
                  }`}
                />
                {errors.departureDate && (
                  <p className="mt-1 text-sm text-charcoal-gold">{errors.departureDate}</p>
                )}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-charcoal mb-2">
                Flight Details
              </label>
              <textarea
                value={formData.flightDetails}
                onChange={(e) => handleInputChange('flightDetails', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-stone-light  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold"
                placeholder="Flight numbers, arrival/departure times..."
              />
            </div>
            
            <div>
              <h3 className="text-lg font-cormorant font-medium text-charcoal mb-sm">
                Emergency Contact
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-sm">
                <div>
                  <label className="block text-sm font-medium text-charcoal mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    value={formData.emergencyName}
                    onChange={(e) => handleInputChange('emergencyName', e.target.value)}
                    className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                      errors.emergencyName ? 'border-charcoal-gold' : 'border-stone-light'
                    }`}
                    placeholder="Emergency contact name"
                  />
                  {errors.emergencyName && (
                    <p className="mt-1 text-sm text-charcoal-gold">{errors.emergencyName}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-charcoal mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    value={formData.emergencyPhone}
                    onChange={(e) => handleInputChange('emergencyPhone', e.target.value)}
                    className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                      errors.emergencyPhone ? 'border-charcoal-gold' : 'border-stone-light'
                    }`}
                    placeholder="Emergency contact phone"
                  />
                  {errors.emergencyPhone && (
                    <p className="mt-1 text-sm text-charcoal-gold">{errors.emergencyPhone}</p>
                  )}
                </div>
              </div>
              
              <div className="mt-sm">
                <label className="block text-sm font-medium text-charcoal mb-2">
                  Relationship
                </label>
                <select
                  value={formData.emergencyRelation}
                  onChange={(e) => handleInputChange('emergencyRelation', e.target.value)}
                  className="w-full px-3 py-2 border border-stone-light  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold"
                >
                  <option value="">Select relationship</option>
                  <option value="parent">Parent</option>
                  <option value="spouse">Spouse</option>
                  <option value="sibling">Sibling</option>
                  <option value="friend">Friend</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>
          </div>
        );
      
      case 5:
        return (
          <div className="space-y-md">
            <div>
              <h3 className="text-lg font-cormorant font-medium text-charcoal mb-sm">
                Payment Method
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-sm mb-md">
                {paymentOptions.map((option) => (
                  <div
                    key={option.id}
                    className={`border-2 p-4 cursor-pointer transition-all ${
                      formData.paymentMethod === option.id
                        ? 'border-charcoal-gold bg-charcoal-gold/5'
                        : 'border-stone-light hover:border-charcoal-gold/50'
                    }`}
                    onClick={() => handleInputChange('paymentMethod', option.id)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-charcoal">{option.name}</h4>
                      {option.discount > 0 && (
                        <span className="text-sm bg-sage-green text-white px-2 py-1">
                          -€{option.discount}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-stone">{option.description}</p>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="bg-sanctuary p-4">
              <h4 className="font-medium text-charcoal mb-3">Order Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Retreat base price</span>
                  <span>€{retreat?.price || 2800}</span>
                </div>
                <div className="flex justify-between">
                  <span>Accommodation upgrade</span>
                  <span>€{accommodationOptions.find(opt => opt.id === formData.accommodationType)?.price || 0}</span>
                </div>
                {formData.paymentMethod === 'full' && (
                  <div className="flex justify-between text-sage-green">
                    <span>Full payment discount</span>
                    <span>-€100</span>
                  </div>
                )}
                <div className="border-t pt-2 flex justify-between font-medium">
                  <span>Total</span>
                  <span>€{calculateTotal()}</span>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-sm">
              <div>
                <label className="block text-sm font-medium text-charcoal mb-2">
                  Card Number *
                </label>
                <input
                  type="text"
                  value={formData.cardNumber}
                  onChange={(e) => handleInputChange('cardNumber', e.target.value)}
                  className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                    errors.cardNumber ? 'border-charcoal-gold' : 'border-stone-light'
                  }`}
                  placeholder="1234 5678 9012 3456"
                />
                {errors.cardNumber && (
                  <p className="mt-1 text-sm text-charcoal-gold">{errors.cardNumber}</p>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm font-medium text-charcoal mb-2">
                    Expiry *
                  </label>
                  <input
                    type="text"
                    value={formData.expiryDate}
                    onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                    className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                      errors.expiryDate ? 'border-charcoal-gold' : 'border-stone-light'
                    }`}
                    placeholder="MM/YY"
                  />
                  {errors.expiryDate && (
                    <p className="mt-1 text-sm text-charcoal-gold">{errors.expiryDate}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-charcoal mb-2">
                    CVV *
                  </label>
                  <input
                    type="text"
                    value={formData.cvv}
                    onChange={(e) => handleInputChange('cvv', e.target.value)}
                    className={`w-full px-3 py-2 border  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold ${
                      errors.cvv ? 'border-charcoal-gold' : 'border-stone-light'
                    }`}
                    placeholder="123"
                  />
                  {errors.cvv && (
                    <p className="mt-1 text-sm text-charcoal-gold">{errors.cvv}</p>
                  )}
                </div>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-charcoal mb-2">
                Billing Address
              </label>
              <textarea
                value={formData.billingAddress}
                onChange={(e) => handleInputChange('billingAddress', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-stone-light  focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold"
                placeholder="Full billing address"
              />
            </div>
            
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  id="terms"
                  checked={formData.termsAccepted}
                  onChange={(e) => handleInputChange('termsAccepted', e.target.checked)}
                  className="mt-1"
                />
                <label htmlFor="terms" className="text-sm text-stone">
                  I accept the{' '}
                  <a href="/terms" className="text-charcoal-gold hover:underline">
                    Terms and Conditions
                  </a>{' '}
                  *
                </label>
              </div>
              {errors.termsAccepted && (
                <p className="text-sm text-charcoal-gold">{errors.termsAccepted}</p>
              )}
              
              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  id="privacy"
                  checked={formData.privacyAccepted}
                  onChange={(e) => handleInputChange('privacyAccepted', e.target.checked)}
                  className="mt-1"
                />
                <label htmlFor="privacy" className="text-sm text-stone">
                  I accept the{' '}
                  <a href="/privacy" className="text-charcoal-gold hover:underline">
                    Privacy Policy
                  </a>{' '}
                  *
                </label>
              </div>
              {errors.privacyAccepted && (
                <p className="text-sm text-charcoal-gold">{errors.privacyAccepted}</p>
              )}
              
              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  id="insurance"
                  checked={formData.travelInsurance}
                  onChange={(e) => handleInputChange('travelInsurance', e.target.checked)}
                  className="mt-1"
                />
                <label htmlFor="insurance" className="text-sm text-stone">
                  I confirm that I have adequate travel insurance
                </label>
              </div>
            </div>
            
            <div className="bg-sage-green/10 p-4 border-l-4 border-sage-green">
              <div className="flex items-center gap-2 mb-2">
                <ShieldCheckIcon className="h-5 w-5 text-sage-green" />
                <h4 className="font-medium text-charcoal">Secure Payment</h4>
              </div>
              <p className="text-sm text-stone">
                Your payment information is encrypted and secure. We use industry-standard 
                SSL encryption to protect your data.
              </p>
            </div>
          </div>
        );
      
      case 6:
        return (
          <div className="text-center space-y-md">
            <div className="w-20 h-20 bg-sage-green rectangular flex items-center justify-center mx-auto">
              <CheckCircleIcon className="h-12 w-12 text-white" />
            </div>
            
            <div>
              <h3 className="text-2xl font-cormorant font-medium text-charcoal mb-2 /* TODO: Replace with SectionTitle */" /* TODO: Replace with CardTitle */>
                Booking Confirmed!
              </h3>
              <p className="text-stone">
                Thank you for booking your retreat with us. We'll send you a confirmation 
                email shortly with all the details.
              </p>
            </div>
            
            <div className="bg-sanctuary p-6 text-left">
              <h4 className="font-medium text-charcoal mb-sm">What's Next?</h4>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircleIcon className="h-5 w-5 text-sage-green mt-0.5" />
                  <div>
                    <p className="font-medium text-charcoal">Confirmation Email</p>
                    <p className="text-sm text-stone">
                      You'll receive a detailed confirmation within 24 hours
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <CheckCircleIcon className="h-5 w-5 text-sage-green mt-0.5" />
                  <div>
                    <p className="font-medium text-charcoal">Pre-Retreat Guide</p>
                    <p className="text-sm text-stone">
                      Detailed preparation guide 2 weeks before departure
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <CheckCircleIcon className="h-5 w-5 text-sage-green mt-0.5" />
                  <div>
                    <p className="font-medium text-charcoal">Personal Support</p>
                    <p className="text-sm text-stone">
                      Julia will contact you personally before the retreat
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex gap-sm justify-center">
              <button
                onClick={() => window.print()}
                className="px-hero-padding py-2 border border-charcoal-gold text-charcoal-gold hover:bg-charcoal-gold hover:text-white transition-colors"
              >
                Print Confirmation
              </button>
              <button
                onClick={() => window.location.href = '/'}
                className="px-hero-padding py-2 bg-charcoal-gold text-white hover:bg-charcoal-gold/90 transition-colors"
              >
                Return to Home
              </button>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      {/* Progress Bar */}
      <div className="mb-lg">
        <div className="flex items-center justify-between mb-sm">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={`w-10 h-10 rectangular flex items-center justify-center border-2 ${
                  currentStep >= step.id
                    ? 'bg-charcoal-gold border-charcoal-gold text-white'
                    : 'border-stone-light text-stone'
                }`}
              >
                {currentStep > step.id ? (
                  <CheckCircleIcon className="h-6 w-6" />
                ) : (
                  <step.icon className="h-5 w-5" />
                )}
              </div>
              
              {index < steps.length - 1 && (
                <div
                  className={`w-16 h-0.5 mx-2 ${
                    currentStep > step.id ? 'bg-charcoal-gold' : 'bg-stone-light'
                  }`}
                />
              )}
            </div>
          ))}
        </div>
        
        <div className="text-center">
          <h2 className="text-xl font-cormorant font-medium text-charcoal /* TODO: Replace with CardTitle */">
            {steps[currentStep - 1]?.title}
          </h2>
          <p className="text-stone text-sm">
            {steps[currentStep - 1]?.description}
          </p>
        </div>
      </div>

      {/* Form Content */}
      <div className="bg-white p-8  shadow-lg">
        {renderStep()}
        
        {errors.submit && (
          <div className="mt-md p-4 bg-charcoal-gold/5 border-l-4 border-charcoal-gold">
            <div className="flex items-center gap-2">
              <ExclamationTriangleIcon className="h-5 w-5 text-charcoal-gold" />
              <p className="text-sm text-red-700">{errors.submit}</p>
            </div>
          </div>
        )}
      </div>

      {/* Navigation */}
      {currentStep < 6 && (
        <div className="flex justify-between mt-lg">
          <button
            onClick={handlePreviousStep}
            disabled={currentStep === 1}
            className={`px-hero-padding py-3 border transition-colors ${
              currentStep === 1
                ? 'border-stone-light text-stone cursor-not-allowed'
                : 'border-charcoal-gold text-charcoal-gold hover:bg-charcoal-gold hover:text-white'
            }`}
          >
            Previous
          </button>
          
          {currentStep < 5 ? (
            <button
              onClick={handleNextStep}
              className="px-hero-padding py-3 bg-charcoal-gold text-white hover:bg-charcoal-gold/90 transition-colors"
            >
              Next
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="px-hero-padding py-3 bg-charcoal-gold text-white hover:bg-charcoal-gold/90 transition-colors disabled:opacity-50"
            >
              {isSubmitting ? 'Processing...' : 'Complete Booking'}
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default EnhancedBookingFlow;