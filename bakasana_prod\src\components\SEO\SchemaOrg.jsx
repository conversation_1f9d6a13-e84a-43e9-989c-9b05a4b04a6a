'use client';

import { useEffect  } from 'react';

export default function SchemaOrg({
  type = 'WebSite',
  data = {},
}) {
  useEffect(() => {
    // Usuń istniejące skrypty schema.org
    const existingScripts = document.querySelectorAll('script[type="application/ld+json"]');
    existingScripts.forEach(script => script.remove());
    
    // Podstawowe dane
    const siteName = process.env.NEXT_PUBLIC_SITE_NAME || 'Wycieczki Bali';
    const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://wycieczki-bali.pl';
    
    // Przygotuj dane strukturalne w zależności od typu
    let schemaData = {};
    
    switch (type) {
      case 'WebSite':
        schemaData = {
          '@context': 'https://schema.org',
          '@type': 'WebSite',
          name: siteName,
          url: siteUrl,
          potentialAction: {
            '@type': 'SearchAction',
            target: `${siteUrl}/search?q={search_term_string}`,
            'query-input': 'required name=search_term_string'
          },
          ...data
        };
        break;
        
      case 'BlogPosting':
        schemaData = {
          '@context': 'https://schema.org',
          '@type': 'BlogPosting',
          headline: data.title,
          image: data.image,
          datePublished: data.datePublished,
          dateModified: data.dateModified || data.datePublished,
          author: {
            '@type': 'Person',
            name: data.author
          },
          publisher: {
            '@type': 'Organization',
            name: siteName,
            logo: {
              '@type': 'ImageObject',
              url: `${siteUrl}/logo.png`
            }
          },
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': `${siteUrl}/blog/${data.slug}`
          },
          ...data
        };
        break;
        
      case 'Person':
        schemaData = {
          '@context': 'https://schema.org',
          '@type': 'Person',
          name: data.name,
          url: `${siteUrl}/o-mnie`,
          ...data
        };
        break;
        
      default:
        schemaData = {
          '@context': 'https://schema.org',
          '@type': type,
          ...data
        };
    }
    
    // Dodaj skrypt z danymi strukturalnymi
    const script = document.createElement('script');
    script.setAttribute('type', 'application/ld+json');
    script.textContent = JSON.stringify(schemaData);
    document.head.appendChild(script);
    
  }, [type, data]);
  
  return null;
}