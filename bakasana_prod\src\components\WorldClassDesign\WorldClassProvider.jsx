
'use client';
import React, { createContext, useContext, useEffect, useState } from 'react';

import AmbientBackground from './AmbientBackground';
import ContextualCursor from './ContextualCursor';
import KeyboardShortcuts from './KeyboardShortcuts';
import ProgressIndicator from './ProgressIndicator';
import SmoothScrollIndicator from './SmoothScrollIndicator';

export const useWorldClass = () => {
export const WorldClassContext = createContext({});



/**
 * 🏆 WORLD CLASS DESIGN PROVIDER - TOP 1% QUALITY
 * Zarządza wszystkimi premium features w jednym miejscu
 * Inspirowane przez Apple, Linear.app, Stripe, Awwwards SOTD
 */


  const context = useContext(WorldClassContext);
  if (!context) {
    throw new Error('useWorldClass must be used within WorldClassProvider');
  }
  return context;
};

const WorldClassProvider = ({ 
  children,
  config = {},
  className = '',
  ...props 
}) => {
  const [isEnabled, setIsEnabled] = useState(true);
  const [features, setFeatures] = useState({
    ambientBackground: true,
    smoothScrollIndicator: true,
    progressIndicator: true,
    contextualCursor: true,
    keyboardShortcuts: true,
    performanceMonitoring: true,
    accessibilityEnhancements: true,
    ...config.features,
  });

  // Performance monitoring
  const [performanceMetrics, setPerformanceMetrics] = useState({
    loadTime: 0,
    firstPaint: 0,
    largestContentfulPaint: 0,
    cumulativeLayoutShift: 0,
    firstInputDelay: 0,
  });

  // Accessibility state
  const [accessibilityMode, setAccessibilityMode] = useState({
    reducedMotion: false,
    highContrast: false,
    dyslexiaMode: false,
    focusVisible: false,
  });

  // Initialize performance monitoring
  useEffect(() => {
    if (!features.performanceMonitoring) return;

    const measurePerformance = () => {
      try {
        const navigation = performance.getEntriesByType('navigation')[0];
        const paint = performance.getEntriesByType('paint');
        
        setPerformanceMetrics({
          loadTime: navigation?.loadEventEnd - navigation?.loadEventStart || 0,
          firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
          largestContentfulPaint: 0, // Will be measured via observer
          cumulativeLayoutShift: 0,
          firstInputDelay: 0,
        });

        // Measure LCP
        if ('PerformanceObserver' in window) {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            setPerformanceMetrics(prev => ({
              ...prev,
              largestContentfulPaint: lastEntry.startTime,
            }));
          });
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

          // Measure CLS
          const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0;
            for (const entry of list.getEntries()) {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            }
            setPerformanceMetrics(prev => ({
              ...prev,
              cumulativeLayoutShift: clsValue,
            }));
          });
          clsObserver.observe({ entryTypes: ['layout-shift'] });

          // Measure FID
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const firstEntry = entries[0];
            setPerformanceMetrics(prev => ({
              ...prev,
              firstInputDelay: firstEntry.processingStart - firstEntry.startTime,
            }));
          });
          fidObserver.observe({ entryTypes: ['first-input'] });
        }
      } catch (error) {
        console.warn('Performance monitoring failed:', error);
      }
    };

    // Measure after page load
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
    }

    return () => {
      window.removeEventListener('load', measurePerformance);
    };
  }, [features.performanceMonitoring]);

  // Initialize accessibility monitoring
  useEffect(() => {
    if (!features.accessibilityEnhancements) return;

    const checkAccessibilityPreferences = () => {
      setAccessibilityMode({
        reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        highContrast: window.matchMedia('(prefers-contrast: high)').matches,
        dyslexiaMode: localStorage.getItem('dyslexia-mode') === 'true',
        focusVisible: false,
      });
    };

    checkAccessibilityPreferences();

    // Listen for changes
    const mediaQueries = [
      window.matchMedia('(prefers-reduced-motion: reduce)'),
      window.matchMedia('(prefers-contrast: high)'),
    ];

    mediaQueries.forEach(mq => {
      mq.addEventListener('change', checkAccessibilityPreferences);
    });

    // Focus visible detection
    const handleKeyDown = (e) => {
      if (e.key === 'Tab') {
        setAccessibilityMode(prev => ({ ...prev, focusVisible: true }));
      }
    };

    const handleMouseDown = () => {
      setAccessibilityMode(prev => ({ ...prev, focusVisible: false }));
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      mediaQueries.forEach(mq => {
        mq.removeEventListener('change', checkAccessibilityPreferences);
      });
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, [features.accessibilityEnhancements]);

  // Apply accessibility styles
  useEffect(() => {
    const applyAccessibilityStyles = () => {
      const style = document.createElement('style');
      style.id = 'world-class-accessibility';
      
      let css = '';
      
      if (accessibilityMode.reducedMotion) {
        css += `
          *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
            scroll-behavior: auto !important;
          }
        `;
      }
      
      if (accessibilityMode.highContrast) {
        css += `
          * {
            filter: contrast(1.2) !important;
          }
        `;
      }
      
      if (accessibilityMode.dyslexiaMode) {
        css += `
          * {
            font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;
          }
        `;
      }
      
      if (accessibilityMode.focusVisible) {
        css += `
          :focus {
            outline: 2px solid #7C9885 !important;
            outline-offset: 2px !important;
          }
        `;
      }
      
      style.textContent = css;
      
      const existingStyle = document.getElementById('world-class-accessibility');
      if (existingStyle) {
        existingStyle.remove();
      }
      
      if (css) {
        document.head.appendChild(style);
      }
    };

    applyAccessibilityStyles();
  }, [accessibilityMode]);

  const contextValue = {
    isEnabled,
    setIsEnabled,
    features,
    setFeatures,
    performanceMetrics,
    accessibilityMode,
    setAccessibilityMode,
    toggleFeature: (featureName) => {
      setFeatures(prev => ({
        ...prev,
        [featureName]: !prev[featureName],
      }));
    },
    toggleDyslexiaMode: () => {
      const newMode = !accessibilityMode.dyslexiaMode;
      localStorage.setItem('dyslexia-mode', newMode.toString());
      setAccessibilityMode(prev => ({ ...prev, dyslexiaMode: newMode }));
    },
  };

  if (!isEnabled) {
    return <div className={className} {...props}>{children}</div>;
  }

  return (
    <WorldClassContext.Provider value={contextValue}>
      <AmbientBackground className={className} {...props}>
        {children}
        
        {/* Premium features */}
        {features.smoothScrollIndicator && <SmoothScrollIndicator />}
        {features.progressIndicator && <ProgressIndicator />}
        {features.contextualCursor && <ContextualCursor />}
        {features.keyboardShortcuts && <KeyboardShortcuts />}
        
        {/* Performance monitoring widget (dev mode) */}
        {process.env.NODE_ENV === 'development' && features.performanceMonitoring && (
          <div
            style={{
              position: 'fixed',
              top: '20px',
              left: '20px',
              background: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              padding: '12px',
              ,
              fontSize: '12px',
              fontFamily: 'monospace',
              zIndex: 9999,
              maxWidth: '300px',
            }}
          >
            <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>Performance</div>
            <div>Load: {performanceMetrics.loadTime.toFixed(0)}ms</div>
            <div>FP: {performanceMetrics.firstPaint.toFixed(0)}ms</div>
            <div>LCP: {performanceMetrics.largestContentfulPaint.toFixed(0)}ms</div>
            <div>CLS: {performanceMetrics.cumulativeLayoutShift.toFixed(3)}</div>
            <div>FID: {performanceMetrics.firstInputDelay.toFixed(0)}ms</div>
          </div>
        )}
        
        {/* Accessibility widget */}
        {features.accessibilityEnhancements && (
          <div
            style={{
              position: 'fixed',
              bottom: '20px',
              right: '20px',
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
              padding: '16px',
              ,
              border: '1px solid rgba(124, 152, 133, 0.2)',
              fontSize: '14px',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
              zIndex: 9998,
            }}
          >
            <div style={{ fontWeight: '600', marginBottom: '8px', color: '#7C9885' }}>
              Accessibility
            </div>
            <label style={{ display: 'block', marginBottom: '4px', fontSize: '12px' }}>
              <input
                type="checkbox"
                checked={accessibilityMode.dyslexiaMode}
                onChange={contextValue.toggleDyslexiaMode}
                style={{ marginRight: '8px' }}
              />
              Dyslexia Mode
            </label>
            <div style={{ fontSize: '10px', color: '#666', marginTop: '8px' }}>
              Press "?" for shortcuts
            </div>
          </div>
        )}
      </AmbientBackground>
    </WorldClassContext.Provider>
  );
};


export default WorldClassProvider;