

import Link from 'next/link';

import { HeroTitle, SectionTitle, CardTitle, BodyText  } from '@/components/ui/UnifiedTypography';

import { BlogImage  } from '@/components/OptimizedImage';

import OptimizedIcon from './OptimizedIcon';

export const RelatedBlogPosts = ({ currentPost, allPosts }) => {
export default InternalLinks;

const InternalLinks = ({ currentPage, className = "" }) => {
  // Enhanced mapa internal links dla różnych stron z SEO focus
  const linkSuggestions = {
    'home': [
      { href: '/retreaty', label: 'Retreaty Jogi', icon: 'Calendar', description: 'Bali & Sri Lanka' },
      { href: '/zajecia-stacjonarne', label: 'Zajęcia Stacjonarne', icon: 'MapPin', description: 'Studio Warszawa' },
      { href: '/zajecia-online', label: 'Zajęcia Online', icon: 'Video', description: 'Joga w domu' },
      { href: '/rezerwacja', label: 'Rezerwa<PERSON><PERSON>', icon: 'Calendar', description: '<PERSON><PERSON><PERSON><PERSON><PERSON> miej<PERSON>ce' }
    ],
    'blog': [
      { href: '/retreaty-jogi-bali-2025', label: 'Retreaty Bali 2025', icon: 'MapPin', description: 'Luksusowe wyjazdy' },
      { href: '/joga-sri-lanka-retreat', label: 'Retreaty Sri Lanka', icon: 'MapPin', description: 'Duchowa podróż' },
      { href: '/zajecia-online', label: 'Zajęcia Online', icon: 'Video', description: 'Joga w domu' },
      { href: '/rezerwacja', label: 'Rezerwacja', icon: 'Calendar', description: 'Zarezerwuj miejsce' }
    ],
    'program': [
      { href: '/retreaty-jogi-bali-2025', label: 'Retreaty Bali', icon: 'MapPin', description: 'Luksusowe wyjazdy' },
      { href: '/joga-sri-lanka-retreat', label: 'Retreaty Sri Lanka', icon: 'MapPin', description: 'Duchowa podróż' },
      { href: '/rezerwacja', label: 'Rezerwacja', icon: 'Calendar', description: 'Zarezerwuj miejsce' },
      { href: '/blog', label: 'Blog o Jodze', icon: 'BookOpen', description: 'Artykuły i porady' }
    ],
    'retreaty': [
      { href: '/retreaty-jogi-bali-2025', label: 'Retreaty Bali 2025', icon: 'MapPin', description: 'Luksusowe wyjazdy' },
      { href: '/joga-sri-lanka-retreat', label: 'Retreaty Sri Lanka', icon: 'MapPin', description: 'Duchowa podróż' },
      { href: '/rezerwacja', label: 'Rezerwacja', icon: 'Calendar', description: 'Zarezerwuj miejsce' },
      { href: '/zajecia-stacjonarne', label: 'Zajęcia Stacjonarne', icon: 'MapPin', description: 'Studio Warszawa' }
    ],
    'zajecia-stacjonarne': [
      { href: '/zajecia-online', label: 'Zajęcia Online', icon: 'Video', description: 'Joga w domu' },
      { href: '/retreaty', label: 'Retreaty Jogi', icon: 'MapPin', description: 'Bali & Sri Lanka' },
      { href: '/julia-jakubowicz-instruktor', label: 'O Instruktorze', icon: 'User', description: 'Doświadczenie' },
      { href: '/kontakt', label: 'Kontakt', icon: 'Mail', description: 'Skontaktuj się' }
    ],
    'zajecia-online': [
      { href: '/zajecia-stacjonarne', label: 'Zajęcia Stacjonarne', icon: 'MapPin', description: 'Studio Warszawa' },
      { href: '/retreaty', label: 'Retreaty Jogi', icon: 'MapPin', description: 'Bali & Sri Lanka' },
      { href: '/blog', label: 'Blog o Jodze', icon: 'BookOpen', description: 'Artykuły i porady' },
      { href: '/kontakt', label: 'Zapisz się', icon: 'Mail', description: 'Skontaktuj się' }
    ],
    'rezerwacja': [
      { href: '/retreaty-jogi-bali-2025', label: 'Retreaty Bali', icon: 'MapPin', description: 'Luksusowe wyjazdy' },
      { href: '/joga-sri-lanka-retreat', label: 'Retreaty Sri Lanka', icon: 'MapPin', description: 'Duchowa podróż' },
      { href: '/program', label: 'Program Retreatów', icon: 'Calendar', description: 'Szczegóły wyjazdów' },
      { href: '/kontakt', label: 'Kontakt', icon: 'Mail', description: 'Pomoc w rezerwacji' }
    ],
    'galeria': [
      { href: '/retreaty-jogi-bali-2025', label: 'Retreaty Bali', icon: 'MapPin', description: 'Luksusowe wyjazdy' },
      { href: '/joga-sri-lanka-retreat', label: 'Retreaty Sri Lanka', icon: 'MapPin', description: 'Duchowa podróż' },
      { href: '/blog', label: 'Relacje z Podróży', icon: 'BookOpen', description: 'Artykuły i porady' },
      { href: '/rezerwacja', label: 'Rezerwacja', icon: 'Calendar', description: 'Zarezerwuj miejsce' }
    ],
    'kontakt': [
      { href: '/retreaty', label: 'Retreaty Jogi', icon: 'MapPin', description: 'Bali & Sri Lanka' },
      { href: '/zajecia-stacjonarne', label: 'Zajęcia Stacjonarne', icon: 'MapPin', description: 'Studio Warszawa' },
      { href: '/zajecia-online', label: 'Zajęcia Online', icon: 'Video', description: 'Joga w domu' },
      { href: '/rezerwacja', label: 'Rezerwacja', icon: 'Calendar', description: 'Zarezerwuj miejsce' }
    ],
    'o-mnie': [
      { href: '/julia-jakubowicz-instruktor', label: 'Doświadczenie', icon: 'User', description: 'Kwalifikacje' },
      { href: '/retreaty', label: 'Retreaty Jogi', icon: 'MapPin', description: 'Bali & Sri Lanka' },
      { href: '/zajecia-stacjonarne', label: 'Zajęcia Stacjonarne', icon: 'MapPin', description: 'Studio Warszawa' },
      { href: '/kontakt', label: 'Kontakt', icon: 'Mail', description: 'Skontaktuj się' }
    ]
  };

  const links = linkSuggestions[currentPage] || [];

  if (links.length === 0) return null;

  return (
    <section className={`py-12 ${className}`}>
      <div className="max-w-4xl mx-auto px-container-sm sm:px-hero-padding lg:px-hero-padding">
        <div className="bg-gradient-to-r from-charcoal/5 to-bamboo/5 rectangular p-8 border border-enterprise-brown/10">
          <h3 className="text-xl font-cormorant text-enterprise-brown mb-md text-center /* TODO: Replace with CardTitle */">
            Może Cię również zainteresować
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-sm">
            {links.map((link, index) => (
              <Link
                key={index}
                href={link.href}
                className="group flex flex-col gap-2 p-4 bg-silk/60 backdrop-blur-sm rectangular border border-enterprise-brown/10 hover:border-enterprise-brown/20 hover:shadow-medium transition-all duration-300 hover:-translate-y-1"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-enterprise-brown/10 rectangular group-hover:bg-enterprise-brown/20 transition-colors duration-300">
                    <OptimizedIcon 
                      name={link.icon} 
                      className="w-4 h-4 text-enterprise-brown" 
                    />
                  </div>
                  <div className="flex-1">
                    <div className="text-enterprise-brown font-medium text-sm group-hover:text-terra transition-colors duration-300">
                      {link.label}
                    </div>
                    {link.description && (
                      <div className="text-xs text-charcoal-light/60 group-hover:text-charcoal-light transition-colors duration-300">
                        {link.description}
                      </div>
                    )}
                  </div>
                  <OptimizedIcon 
                    name="ArrowRight" 
                    className="w-4 h-4 text-enterprise-brown/40 group-hover:text-enterprise-brown group-hover:translate-x-1 transition-all duration-300" 
                  />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

// Komponent dla related blog posts z enhanced linking
  // Znajdź powiązane posty na podstawie tagów
  const relatedPosts = allPosts
    .filter(post => 
      post.slug !== currentPost.slug && 
      post.tags?.some(tag => currentPost.tags?.includes(tag))
    )
    .slice(0, 3);

  if (relatedPosts.length === 0) return null;

  return (
    <section className="py-12">
      <div className="max-w-4xl mx-auto px-container-sm sm:px-hero-padding lg:px-hero-padding">
        <div className="text-center mb-lg">
          <SectionTitle level={3}>Powiązane Artykuły</SectionTitle>
          <p className="text-charcoal-light/80 text-sm">Kontynuuj czytanie na podobne tematy</p>
          <div className="w-12 h-0.5 bg-enterprise-brown/20 mx-auto mt-sm"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-md">
          {relatedPosts.map(post => (
            <Link
              href={`/blog/${post.slug}`}
              key={post.slug}
              className="group bg-silk/60 backdrop-blur-sm rectangular border border-enterprise-brown/10 overflow-hidden hover:shadow-medium transition-all duration-300 hover:-translate-y-1"
            >
              {post.imageUrl && (
                <div className="relative h-40 overflow-hidden">
                  <BlogImage
                    src={post.imageUrl}
                    alt={post.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-charcoal/20 to-transparent"></div>
                </div>
              )}
              <div className="p-4">
                <h4 className="font-cormorant text-enterprise-brown text-lg leading-tight group-hover:text-terra transition-colors duration-300 mb-2">
                  {post.title}
                </h4>
                <p className="text-charcoal-light/70 text-sm line-clamp-2 mb-3">
                  {post.excerpt}
                </p>
                <div className="flex items-center gap-2 text-xs text-enterprise-brown/60">
                  <OptimizedIcon name="Calendar" className="w-3 h-3" />
                  <span>{new Date(post.date).toLocaleDateString('pl-PL')}</span>
                  <span>•</span>
                  <span>{post.category}</span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

