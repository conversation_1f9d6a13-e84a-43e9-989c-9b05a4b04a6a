'use client';

import React from 'react';

// Zen-inspired loading animation
export const ZenLoader = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-16 h-16',
    lg: 'w-24 h-24',
    xl: 'w-32 h-32'
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className={`relative ${sizeClasses[size]}`}>
        {/* Outer circle */}
        <div className="absolute inset-0 border-2 border-charcoal-gold/20 rectangular animate-spin" />
        
        {/* Inner circle */}
        <div className="absolute inset-2 border-2 border-sage-green/30 rectangular animate-spin" style={{
          animationDirection: 'reverse',
          animationDuration: '1.5s'
        }} />
        
        {/* Center dot */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-2 h-2 bg-charcoal-gold rectangular animate-pulse" />
        </div>
      </div>
    </div>
  );
};

// Lotus-inspired loading animation
export const LotusLoader = ({ className = '' }) => {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="relative w-16 h-16">
        {/* Petals */}
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-6 bg-charcoal-gold rectangular opacity-30"
            style={{
              top: '20px',
              left: '31px',
              transformOrigin: '1px 12px',
              transform: `rotate(${i * 45}deg)`,
              animation: `lotus-petal 2s ease-in-out infinite`,
              animationDelay: `${i * 0.1}s`
            }}
          />
        ))}
        
        {/* Center */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-4 h-4 bg-sage-green rectangular animate-pulse" />
        </div>
      </div>
      
      <style jsx>{`
        @keyframes lotus-petal {
          0%, 100% { opacity: 0.3; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.2); }
        }
      `}</style>
    </div>
  );
};

// Minimalist line loader
export const LineLoader = ({ className = '' }) => {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="w-32 h-1 bg-stone-light rectangular overflow-hidden">
        <div 
          className="h-full bg-gradient-to-r from-charcoal-gold to-sage-green rectangular"
          style={{
            animation: 'line-load 2s ease-in-out infinite',
            width: '30%'
          }}
        />
      </div>
      
      <style jsx>{`
        @keyframes line-load {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(300%); }
        }
      `}</style>
    </div>
  );
};

// Breathing loader (like meditation)
export const BreathingLoader = ({ className = '' }) => {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="relative">
        <div 
          className="w-20 h-20 border-2 border-charcoal-gold/30 rectangular"
          style={{
            animation: 'breathing 3s ease-in-out infinite'
          }}
        />
        
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-sm font-cormorant text-charcoal-gold animate-pulse">
            breathe
          </span>
        </div>
      </div>
      
      <style jsx>{`
        @keyframes breathing {
          0%, 100% { transform: scale(1); opacity: 0.5; }
          50% { transform: scale(1.1); opacity: 1; }
        }
      `}</style>
    </div>
  );
};

// Skeleton loaders for different content types
export const SkeletonCard = ({ className = '' }) => {
  return (
    <div className={`bg-white p-6  shadow-lg animate-pulse ${className}`}>
      <div className="w-full h-48 bg-stone-light  mb-sm" />
      <div className="space-y-3">
        <div className="h-4 bg-stone-light w-3/4" />
        <div className="h-4 bg-stone-light w-1/2" />
        <div className="h-4 bg-stone-light w-5/6" />
      </div>
    </div>
  );
};

export const SkeletonText = ({ lines = 3, className = '' }) => {
  return (
    <div className={`animate-pulse ${className}`}>
      {[...Array(lines)].map((_, i) => (
        <div
          key={i}
          className="h-4 bg-stone-light mb-3"
          style={{
            width: i === lines - 1 ? '60%' : '100%'
          }}
        />
      ))}
    </div>
  );
};

export const SkeletonImage = ({ className = '' }) => {
  return (
    <div className={`bg-stone-light animate-pulse ${className}`}>
      <div className="w-full h-full flex items-center justify-center text-stone">
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>
    </div>
  );
};

// Loading state for retreat cards
export const RetreatCardSkeleton = ({ className = '' }) => {
  return (
    <div className={`bg-white  shadow-lg overflow-hidden animate-pulse ${className}`}>
      <div className="w-full h-64 bg-stone-light" />
      <div className="p-6">
        <div className="flex items-center gap-2 mb-3">
          <div className="w-4 h-4 bg-stone-light rectangular" />
          <div className="h-3 bg-stone-light w-24" />
        </div>
        <div className="h-6 bg-stone-light w-3/4 mb-3" />
        <div className="h-4 bg-stone-light w-1/2 mb-sm" />
        <div className="space-y-2 mb-sm">
          <div className="h-3 bg-stone-light w-full" />
          <div className="h-3 bg-stone-light w-4/5" />
          <div className="h-3 bg-stone-light w-2/3" />
        </div>
        <div className="flex items-center justify-between">
          <div className="h-4 bg-stone-light w-20" />
          <div className="h-6 bg-stone-light w-16" />
        </div>
      </div>
    </div>
  );
};

// Loading state for testimonials
export const TestimonialSkeleton = ({ className = '' }) => {
  return (
    <div className={`bg-white p-6  shadow-lg animate-pulse ${className}`}>
      <div className="flex items-center gap-sm mb-sm">
        <div className="w-12 h-12 bg-stone-light rectangular" />
        <div>
          <div className="h-4 bg-stone-light w-24 mb-2" />
          <div className="h-3 bg-stone-light w-16" />
        </div>
      </div>
      <div className="space-y-2">
        <div className="h-4 bg-stone-light w-full" />
        <div className="h-4 bg-stone-light w-5/6" />
        <div className="h-4 bg-stone-light w-3/4" />
      </div>
    </div>
  );
};

// Loading state for navigation
export const NavigationSkeleton = ({ className = '' }) => {
  return (
    <nav className={`bg-white shadow-lg animate-pulse ${className}`}>
      <div className="max-w-7xl mx-auto px-container-sm sm:px-hero-padding lg:px-hero-padding">
        <div className="flex justify-between items-center h-16">
          <div className="h-8 bg-stone-light w-32" />
          <div className="flex space-x-8">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-4 bg-stone-light w-20" />
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
};

// Loading state for forms
export const FormSkeleton = ({ className = '' }) => {
  return (
    <div className={`bg-white p-6  shadow-lg animate-pulse ${className}`}>
      <div className="h-6 bg-stone-light w-1/3 mb-md" />
      <div className="space-y-sm">
        {[...Array(4)].map((_, i) => (
          <div key={i}>
            <div className="h-4 bg-stone-light w-24 mb-2" />
            <div className="h-10 bg-stone-light w-full" />
          </div>
        ))}
      </div>
      <div className="mt-md h-12 bg-stone-light w-32" />
    </div>
  );
};

// Higher-order component for loading states
export const withLoadingState = (Component, LoadingComponent = ZenLoader) => {
  return ({ isLoading, ...props }) => {
    if (isLoading) {
      return <LoadingComponent />;
    }
    return <Component {...props} />;
  };
};

// Loading provider context
export const LoadingContext = React.createContext({
  isLoading: false,
  setLoading: () => {}
});

export const LoadingProvider = ({ children }) => {
  const [isLoading, setIsLoading] = React.useState(false);
  
  const setLoading = (loading) => {
    setIsLoading(loading);
  };
  
  return (
    <LoadingContext.Provider value={{ isLoading, setLoading }}>
      {children}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8  shadow-2xl">
            <ZenLoader size="lg" />
            <p className="mt-sm text-center text-stone font-cormorant">
              Loading your journey...
            </p>
          </div>
        </div>
      )}
    </LoadingContext.Provider>
  );
};

// Hook for using loading context
export const useLoading = () => {
  const context = React.useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

export default ZenLoader;