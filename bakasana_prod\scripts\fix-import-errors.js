#!/usr/bin/env node

/**
 * BAKASANA - IMPORT SYNTAX ERROR FIXER
 * Na<PERSON><PERSON>a błędy składniowe w importach powstałe podczas migracji
 */

const fs = require('fs');
const path = require('path');

class ImportFixer {
  constructor() {
    this.srcPath = path.join(__dirname, '../src');
    this.fixedFiles = [];
    this.errors = [];
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',
      success: '\x1b[32m',
      warning: '\x1b[33m',
      error: '\x1b[31m',
      reset: '\x1b[0m'
    };
    
    console.log(`${colors[type]}[${type.toUpperCase()}]${colors.reset} ${message}`);
  }

  fixImportErrors(file, content) {
    let modified = false;
    let lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Pattern: import X from 'import { Y } from 'Z';
      // Should be: import X from 'A'; import { Y } from 'Z';
      const brokenImportPattern = /^import\s+(.+?)\s+from\s+'import\s+\{([^}]+)\}\s+from\s+'([^']+)';$/;
      const match = line.match(brokenImportPattern);
      
      if (match) {
        const [, firstImport, secondImport, secondPath] = match;
        
        // Znajdź następną linię która może zawierać resztę pierwszego importu
        let nextLine = '';
        if (i + 1 < lines.length) {
          nextLine = lines[i + 1];
        }
        
        // Sprawdź czy następna linia to reszta pierwszego importu
        if (nextLine && !nextLine.startsWith('import') && nextLine.includes("';")) {
          const firstPath = nextLine.replace("';", '').trim();
          
          // Napraw importy
          lines[i] = `import ${firstImport} from '${firstPath}';`;
          lines[i + 1] = `import { ${secondImport} } from '${secondPath}';`;
          
          modified = true;
          this.log(`Fixed broken import in line ${i + 1}`, 'success');
        }
      }

      // Pattern: import { X } from 'import { Y } from 'Z';
      const brokenImportPattern2 = /^import\s+\{([^}]+)\}\s+from\s+'import\s+\{([^}]+)\}\s+from\s+'([^']+)';$/;
      const match2 = line.match(brokenImportPattern2);
      
      if (match2) {
        const [, firstImport, secondImport, secondPath] = match2;
        
        // Napraw na dwa oddzielne importy
        lines[i] = `import { ${secondImport} } from '${secondPath}';`;
        lines.splice(i + 1, 0, `import { ${firstImport} } from '@/components/ui/UnifiedButton';`);
        
        modified = true;
        this.log(`Fixed broken import in line ${i + 1}`, 'success');
      }

      // Pattern dla innych błędnych importów
      if (line.includes("import.*from.*import") || 
          (line.includes('import ') && line.includes(' from ') && !line.includes(';') && line.includes('import '))) {
        
        // Spróbuj naprawić automatycznie
        if (line.includes('UnifiedButton')) {
          // Dodaj brakujący import UnifiedButton
          const cleanLine = line.replace(/import.*from.*import.*UnifiedButton.*/, '').trim();
          if (cleanLine) {
            lines[i] = cleanLine;
            lines.splice(i + 1, 0, "import { UnifiedButton } from '@/components/ui/UnifiedButton';");
            modified = true;
          }
        } else if (line.includes('Icon')) {
          // Dodaj brakujący import Icon
          const cleanLine = line.replace(/import.*from.*import.*Icon.*/, '').trim();
          if (cleanLine) {
            lines[i] = cleanLine;
            lines.splice(i + 1, 0, "import { Icon } from '@/components/ui/IconSystem';");
            modified = true;
          }
        }
      }
    }

    if (modified) {
      return lines.join('\n');
    }
    return null;
  }

  getAllFiles(dir, extensions) {
    let files = [];
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files = files.concat(this.getAllFiles(fullPath, extensions));
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
    
    return files;
  }

  async run() {
    this.log('🔧 Naprawiam błędy składniowe w importach...', 'info');
    
    const files = this.getAllFiles(this.srcPath, ['.jsx', '.tsx', '.js', '.ts']);
    
    for (const file of files) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        
        // Sprawdź czy plik ma błędy składniowe w importach
        if (content.includes("import.*from.*import") || 
            content.match(/import\s+.+\s+from\s+'import/)) {
          
          const fixedContent = this.fixImportErrors(file, content);
          
          if (fixedContent) {
            fs.writeFileSync(file, fixedContent);
            this.fixedFiles.push(file);
            this.log(`✓ ${path.relative(this.srcPath, file)}`, 'success');
          }
        }
        
      } catch (error) {
        this.errors.push(`Error fixing ${file}: ${error.message}`);
        this.log(`✗ ${path.relative(this.srcPath, file)}: ${error.message}`, 'error');
      }
    }

    this.generateReport();
  }

  generateReport() {
    this.log('\n📊 Raport naprawy importów:', 'info');
    this.log(`✅ Naprawione pliki: ${this.fixedFiles.length}`, 'success');
    
    if (this.errors.length > 0) {
      this.log(`❌ Błędy: ${this.errors.length}`, 'error');
      this.errors.forEach(error => this.log(`  - ${error}`, 'error'));
    }

    if (this.fixedFiles.length > 0) {
      this.log('\n🎉 Błędy składniowe zostały naprawione!', 'success');
      this.log('🚀 Możesz teraz uruchomić aplikację', 'info');
    } else {
      this.log('\n✨ Nie znaleziono błędów składniowych do naprawienia', 'success');
    }
  }
}

// Uruchom fixer
if (require.main === module) {
  const fixer = new ImportFixer();
  fixer.run();
}

module.exports = ImportFixer;