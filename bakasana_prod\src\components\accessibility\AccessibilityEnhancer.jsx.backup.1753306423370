'use client';

import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

/**
 * ♿ AccessibilityEnhancer - WCAG 2.1 AAA Compliant
 * 
 * Features:
 * - Focus management
 * - Keyboard navigation
 * - Screen reader support
 * - High contrast mode
 * - Font size adjustment
 * - Motion reduction
 * - Skip links
 * - Live regions
 */

export default function AccessibilityEnhancer({ children }) {
  const [settings, setSettings] = useState({
    highContrast: false,
    fontSize: 100,
    reducedMotion: false,
    focusVisible: true,
    screenReaderMode: false
  });

  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [announcements, setAnnouncements] = useState([]);
  const skipLinksRef = useRef(null);
  const liveRegionRef = useRef(null);

  // Initialize accessibility settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('bakasana-accessibility');
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }

    // Detect user preferences
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;

    if (prefersReducedMotion || prefersHighContrast) {
      setSettings(prev => ({
        ...prev,
        reducedMotion: prefersReducedMotion,
        highContrast: prefersHighContrast
      }));
    }
  }, []);

  // Apply settings to document
  useEffect(() => {
    const root = document.documentElement;
    
    // High contrast mode
    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Font size adjustment
    root.style.fontSize = `${settings.fontSize}%`;

    // Reduced motion
    if (settings.reducedMotion) {
      root.classList.add('reduce-motion');
    } else {
      root.classList.remove('reduce-motion');
    }

    // Focus visible
    if (settings.focusVisible) {
      root.classList.add('focus-visible-enabled');
    } else {
      root.classList.remove('focus-visible-enabled');
    }

    // Save to localStorage
    localStorage.setItem('bakasana-accessibility', JSON.stringify(settings));
  }, [settings]);

  // Keyboard navigation handler
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Alt + M: Skip to main content
      if (e.altKey && e.key === 'm') {
        e.preventDefault();
        const main = document.querySelector('main');
        if (main) {
          main.focus();
          announce('Przeskoczono do głównej treści');
        }
      }

      // Alt + N: Skip to navigation
      if (e.altKey && e.key === 'n') {
        e.preventDefault();
        const nav = document.querySelector('nav');
        if (nav) {
          nav.focus();
          announce('Przeskoczono do nawigacji');
        }
      }

      // Alt + H: Skip to main heading
      if (e.altKey && e.key === 'h') {
        e.preventDefault();
        const heading = document.querySelector('h1');
        if (heading) {
          heading.focus();
          announce('Przeskoczono do głównego nagłówka');
        }
      }

      // Alt + C: Toggle high contrast
      if (e.altKey && e.key === 'c') {
        e.preventDefault();
        toggleHighContrast();
      }

      // Escape: Close accessibility menu
      if (e.key === 'Escape' && isMenuOpen) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isMenuOpen]);

  // Announcement function for screen readers
  const announce = (message) => {
    setAnnouncements(prev => [...prev, { id: Date.now(), message }]);
    
    // Remove announcement after 5 seconds
    setTimeout(() => {
      setAnnouncements(prev => prev.slice(1));
    }, 5000);
  };

  const toggleHighContrast = () => {
    setSettings(prev => ({ ...prev, highContrast: !prev.highContrast }));
    announce(settings.highContrast ? 'Wyłączono wysoki kontrast' : 'Włączono wysoki kontrast');
  };

  const adjustFontSize = (delta) => {
    setSettings(prev => ({
      ...prev,
      fontSize: Math.max(75, Math.min(150, prev.fontSize + delta))
    }));
    announce(`Rozmiar czcionki: ${settings.fontSize + delta}%`);
  };

  const toggleReducedMotion = () => {
    setSettings(prev => ({ ...prev, reducedMotion: !prev.reducedMotion }));
    announce(settings.reducedMotion ? 'Włączono animacje' : 'Wyłączono animacje');
  };

  return (
    <>
      {/* Skip Links */}
      <div ref={skipLinksRef} className="skip-links">
        <a 
          href="#main-content" 
          className="skip-link"
          onFocus={() => announce('Link pomijający do głównej treści')}
        >
          Przejdź do głównej treści
        </a>
        <a 
          href="#navigation" 
          className="skip-link"
          onFocus={() => announce('Link pomijający do nawigacji')}
        >
          Przejdź do nawigacji
        </a>
        <a 
          href="#footer" 
          className="skip-link"
          onFocus={() => announce('Link pomijający do stopki')}
        >
          Przejdź do stopki
        </a>
      </div>

      {/* Accessibility Menu Button */}
      <button
        onClick={() => setIsMenuOpen(!isMenuOpen)}
        className={cn(
          "fixed bottom-4 left-4 z-50 w-14 h-14 bg-enterprise-brown text-sanctuary",
          "flex items-center justify-center shadow-lg hover:bg-terra",
          "transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-enterprise-brown",
          "touch-target-comfortable"
        )}
        aria-label="Otwórz menu dostępności"
        aria-expanded={isMenuOpen}
        aria-controls="accessibility-menu"
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="12" cy="12" r="3"/>
          <path d="M12 1v6m0 6v6"/>
          <path d="m21 12-6-3-6 3-6-3"/>
        </svg>
      </button>

      {/* Accessibility Menu */}
      {isMenuOpen && (
        <div
          id="accessibility-menu"
          className={cn(
            "fixed bottom-20 left-4 z-50 w-80 max-w-[calc(100vw-2rem)]",
            "bg-sanctuary border border-stone/20 shadow-2xl p-6 space-y-4",
            "animate-fade-in-scale"
          )}
          role="dialog"
          aria-labelledby="accessibility-menu-title"
          aria-modal="true"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 id="accessibility-menu-title" className="text-lg font-cormorant font-light text-charcoal">
              Ustawienia dostępności
            </h2>
            <button
              onClick={() => setIsMenuOpen(false)}
              className="p-2 text-charcoal hover:text-enterprise-brown transition-colors touch-target"
              aria-label="Zamknij menu dostępności"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </button>
          </div>

          {/* High Contrast Toggle */}
          <div className="flex items-center justify-between">
            <label htmlFor="high-contrast" className="text-sm font-inter text-charcoal">
              Wysoki kontrast
            </label>
            <button
              id="high-contrast"
              onClick={toggleHighContrast}
              className={cn(
                "relative w-12 h-6 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-enterprise-brown",
                settings.highContrast ? 'bg-enterprise-brown' : 'bg-stone/30'
              )}
              role="switch"
              aria-checked={settings.highContrast}
              aria-describedby="high-contrast-desc"
            >
              <span
                className={cn(
                  "absolute top-0.5 left-0.5 w-5 h-5 bg-sanctuary transition-transform duration-300",
                  settings.highContrast ? 'translate-x-6' : 'translate-x-0'
                )}
              />
            </button>
          </div>
          <p id="high-contrast-desc" className="text-xs text-sage">
            Zwiększa kontrast kolorów dla lepszej czytelności
          </p>

          {/* Font Size Adjustment */}
          <div>
            <label className="block text-sm font-inter text-charcoal mb-2">
              Rozmiar czcionki: {settings.fontSize}%
            </label>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => adjustFontSize(-10)}
                className="px-3 py-2 bg-stone/20 text-charcoal hover:bg-stone/30 transition-colors touch-target"
                aria-label="Zmniejsz czcionkę"
                disabled={settings.fontSize <= 75}
              >
                A-
              </button>
              <div className="flex-1 bg-stone/20 h-2 relative">
                <div
                  className="absolute top-0 left-0 h-full bg-enterprise-brown transition-all duration-300"
                  style={{ width: `${((settings.fontSize - 75) / 75) * 100}%` }}
                />
              </div>
              <button
                onClick={() => adjustFontSize(10)}
                className="px-3 py-2 bg-stone/20 text-charcoal hover:bg-stone/30 transition-colors touch-target"
                aria-label="Zwiększ czcionkę"
                disabled={settings.fontSize >= 150}
              >
                A+
              </button>
            </div>
          </div>

          {/* Reduced Motion Toggle */}
          <div className="flex items-center justify-between">
            <label htmlFor="reduced-motion" className="text-sm font-inter text-charcoal">
              Ograniczone animacje
            </label>
            <button
              id="reduced-motion"
              onClick={toggleReducedMotion}
              className={cn(
                "relative w-12 h-6 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-enterprise-brown",
                settings.reducedMotion ? 'bg-enterprise-brown' : 'bg-stone/30'
              )}
              role="switch"
              aria-checked={settings.reducedMotion}
              aria-describedby="reduced-motion-desc"
            >
              <span
                className={cn(
                  "absolute top-0.5 left-0.5 w-5 h-5 bg-sanctuary transition-transform duration-300",
                  settings.reducedMotion ? 'translate-x-6' : 'translate-x-0'
                )}
              />
            </button>
          </div>
          <p id="reduced-motion-desc" className="text-xs text-sage">
            Wyłącza animacje i efekty ruchu
          </p>

          {/* Keyboard Shortcuts Info */}
          <div className="pt-4 border-t border-stone/20">
            <h3 className="text-sm font-inter font-medium text-charcoal mb-2">
              Skróty klawiszowe:
            </h3>
            <div className="text-xs text-sage space-y-1">
              <p>Alt + M - Przejdź do głównej treści</p>
              <p>Alt + N - Przejdź do nawigacji</p>
              <p>Alt + H - Przejdź do nagłówka</p>
              <p>Alt + C - Przełącz kontrast</p>
              <p>Escape - Zamknij to menu</p>
            </div>
          </div>
        </div>
      )}

      {/* Live Region for Announcements */}
      <div
        ref={liveRegionRef}
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
      >
        {announcements.map(announcement => (
          <div key={announcement.id}>
            {announcement.message}
          </div>
        ))}
      </div>

      {/* Main Content */}
      {children}
    </>
  );
}

// Screen Reader Only utility component
export function ScreenReaderOnly({ children, as: Component = 'span', ...props }) {
  return (
    <Component className="sr-only" {...props}>
      {children}
    </Component>
  );
}

// Focus trap utility
export function FocusTrap({ children, active = true }) {
  const containerRef = useRef(null);

  useEffect(() => {
    if (!active || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }, [active]);

  return (
    <div ref={containerRef}>
      {children}
    </div>
  );
}
