'use client';

import { memo, useEffect, useState  } from 'react';
import Script from 'next/script';

// Lazy-loaded analytics - only essential tracking
const LazyAnalytics = memo(() => {
  const [shouldLoad, setShouldLoad] = useState(false);
  const GA_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;
  const FB_PIXEL_ID = process.env.NEXT_PUBLIC_FB_PIXEL_ID;
  const SENTRY_DSN = process.env.NEXT_PUBLIC_SENTRY_DSN;

  useEffect(() => {
    // Load analytics after user interaction or 3 seconds
    const timer = setTimeout(() => setShouldLoad(true), 3000);
    
    const handleInteraction = () => {
      setShouldLoad(true);
      clearTimeout(timer);
    };

    // Load on first user interaction
    const events = ['mousedown', 'touchstart', 'keydown', 'scroll'];
    events.forEach(event => {
      document.addEventListener(event, handleInteraction, { once: true, passive: true });
    });

    return () => {
      clearTimeout(timer);
      events.forEach(event => {
        document.removeEventListener(event, handleInteraction);
      });
    };
  }, []);

  if (!shouldLoad) return null;

  return (
    <>
      {/* Google Analytics 4 - Essential only */}
      {GA_ID && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${GA_ID}`}
            strategy="afterInteractive"
            onLoad={() => {
              // GA4 loaded
            }}
          />
          <Script
            id="ga4-config"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${GA_ID}', {
                  page_path: window.location.pathname,
                  // Privacy-focused settings
                  anonymize_ip: true,
                  allow_google_signals: false,
                  allow_ad_personalization_signals: false
                });
              `,
            }}
          />
        </>
      )}

      {/* Facebook Pixel - Essential only */}
      {FB_PIXEL_ID && (
        <Script
          id="fb-pixel"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              !function(f,b,e,v,n,t,s)
              {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];t=b.createElement(e);t.async=!0;
              t.src=v;s=b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t,s)}(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
              fbq('init', '${FB_PIXEL_ID}');
              fbq('track', 'PageView');
            `,
          }}
        />
      )}

      {/* Sentry for critical errors only */}
      {shouldLoad && SENTRY_DSN && (
        <Script
          id="sentry-critical"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              !function(e,n,t,r,o,a,s){e[o]=e[o]||function(){(e[o].q=e[o].q||[]).push(arguments)},a=n.createElement(t),s=n.getElementsByTagName(t)[0],a.async=1,a.src=r,s.parentNode.insertBefore(a,s)}(window,document,"script","https://browser.sentry-cdn.com/7.28.1/bundle.min.js","Sentry");
              Sentry.init({
                dsn: "${SENTRY_DSN}",
                environment: "${process.env.NODE_ENV}",
                tracesSampleRate: 0.1,
                beforeSend(event, hint) {
                  // Only critical errors
                  if (event.exception) {
                    const error = event.exception.values[0];
                    if (error.type === 'ChunkLoadError' || 
                        error.type === 'NetworkError' ||
                        error.value?.includes('Non-Error promise rejection')) {
                      return null;
                    }
                  }
                  return event;
                }
              });
            `,
          }}
        />
      )}
    </>
  );
});

LazyAnalytics.displayName = 'LazyAnalytics';

export default LazyAnalytics;