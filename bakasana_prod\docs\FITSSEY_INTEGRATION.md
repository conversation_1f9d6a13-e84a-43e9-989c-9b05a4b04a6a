# Integracja z Fitssey - Dokumentacja

## Przegląd

Projekt BAKASANA został zintegrowany z systemem Fitssey do zarządzania zapisami na zajęcia stacjonarne. Fitssey to profesjonalny system do zarządzania studiami fitness i jogi.

## URL Systemu

**Główny link do zapisów:** https://app.fitssey.com/Flywithbakasana/frontoffice

## Komponenty

### 1. FitsseyIntegration.jsx

Główny komponent do obsługi przekierowań do systemu Fitssey.

**Props:**
- `buttonText` - tekst na przycisku (domyślnie: "Zapisz się przez Fitssey")
- `variant` - wariant przycisku: "primary" | "secondary" 
- `size` - rozmiar: "sm" | "md" | "lg"
- `className` - dodatkowe klasy CSS
- `showInfo` - c<PERSON> poka<PERSON>ć informacje o systemie (domyślnie: false)
- `trackingEvent` - nazwa eventu do śledzenia w analytics

**Przykład użycia:**
```jsx
<FitsseyIntegration
  buttonText="Zapisz się na zajęcia"
  variant="primary"
  size="lg"
  trackingEvent="hero_signup"
  showInfo={true}
/>
```

### 2. FitsseyFloatingButton

Floating button do zapisów, pojawia się po 2 sekundach.

**Props:**
- `position` - pozycja: "bottom-right" | "bottom-left" | "top-right" | "top-left"
- `message` - tekst w tooltip
- `className` - dodatkowe klasy CSS

### 3. FitsseyWidget

Widget do osadzania w sekcjach strony.

**Props:**
- `title` - tytuł widgetu
- `description` - opis
- `className` - dodatkowe klasy CSS

### 4. FitsseySchedule.jsx

Komponent do wyświetlania harmonogramu zajęć (z mock danymi).

**Props:**
- `showPrices` - czy pokazać ceny (domyślnie: true)
- `showAvailability` - czy pokazać dostępność miejsc (domyślnie: true)
- `compact` - kompaktowy widok (domyślnie: false)
- `className` - dodatkowe klasy CSS

## Strony

### /zajecia-stacjonarne

Główna strona zajęć stacjonarnych z pełną integracją Fitssey:

**Sekcje:**
1. **Hero** - główny CTA do zapisów
2. **Rodzaje zajęć** - opis różnych typów zajęć
3. **Harmonogram** - tygodniowy plan zajęć
4. **System zapisów** - informacje o Fitssey
5. **Cennik** - ceny i karnety
6. **Konsultacje** - sesje indywidualne
7. **Kontakt** - informacje o studio

**Floating button** - zawsze widoczny przycisk do zapisów

## API Endpoints

### /api/fitssey/webhook

Webhook endpoint do odbierania powiadomień z Fitssey.

**Obsługiwane eventy:**
- `booking.created` - nowa rezerwacja
- `booking.cancelled` - anulowanie rezerwacji  
- `payment.completed` - zakończona płatność
- `class.updated` - aktualizacja zajęć

**Konfiguracja:**
```env
FITSSEY_WEBHOOK_SECRET=your_webhook_secret_here
```

## Analytics

Wszystkie interakcje z systemem Fitssey są śledzone przez Google Analytics:

**Eventy:**
- `fitssey_redirect` - przekierowanie do systemu
- `hero_signup` - zapis z sekcji hero
- `schedule_signup` - zapis z harmonogramu
- `consultation_booking` - umówienie konsultacji
- `fitssey_floating_button` - kliknięcie floating button

## Nawigacja

Zajęcia stacjonarne zostały dodane do głównej nawigacji:

```
Zajęcia (dropdown)
├── Zajęcia Stacjonarne (highlight)
└── Zajęcia Online
```

## Strona główna

Dodano kartę "Zajęcia Stacjonarne" w sekcji usług z linkiem do `/zajecia-stacjonarne`.

## Przyszłe rozszerzenia

### 1. API Integration
- Pobieranie rzeczywistego harmonogramu z Fitssey API
- Synchronizacja dostępności miejsc
- Automatyczne aktualizacje cen

### 2. Webhook Handlers
- Automatyczne emaile potwierdzające
- Dodawanie klientów do newslettera
- Integracja z CRM

### 3. Advanced Features
- Osadzony kalendarz Fitssey
- Single Sign-On (SSO)
- Personalizowane rekomendacje zajęć

## Testowanie

### Lokalne testowanie
1. Uruchom serwer deweloperski: `npm run dev`
2. Przejdź do `/zajecia-stacjonarne`
3. Przetestuj wszystkie przyciski Fitssey
4. Sprawdź floating button
5. Zweryfikuj tracking w DevTools

### Webhook testowanie
1. Użyj narzędzia jak ngrok do tunelowania
2. Skonfiguruj webhook URL w Fitssey
3. Przetestuj różne eventy
4. Sprawdź logi w konsoli

## Bezpieczeństwo

### Webhook Security
- Weryfikacja podpisu HMAC (do implementacji)
- Rate limiting
- Walidacja danych wejściowych

### Privacy
- Zgodność z RODO
- Minimalizacja zbieranych danych
- Bezpieczne przekierowania (noopener, noreferrer)

## Monitoring

### Metryki do śledzenia
- Liczba przekierowań do Fitssey
- Conversion rate (odwiedziny → zapisy)
- Najpopularniejsze punkty wejścia
- Błędy integracji

### Alerty
- Błędy webhook
- Spadek conversion rate
- Problemy z dostępnością Fitssey

## Wsparcie

W przypadku problemów z integracją:

1. Sprawdź logi w konsoli przeglądarki
2. Zweryfikuj URL Fitssey
3. Przetestuj webhook endpoint
4. Skontaktuj się z supportem Fitssey

## Changelog

### v1.0.0 (2024-12-19)
- Podstawowa integracja z Fitssey
- Komponenty FitsseyIntegration, FitsseyFloatingButton, FitsseyWidget
- Strona /zajecia-stacjonarne
- Webhook endpoint
- Analytics tracking
- Aktualizacja nawigacji