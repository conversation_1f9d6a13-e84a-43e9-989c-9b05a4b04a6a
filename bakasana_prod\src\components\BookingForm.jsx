
'use client';
import { useState  } from 'react';

import { motion, AnimatePresence   } from '@/components/ui/UnifiedButton';
import UnifiedButton from '@/components/ui/UnifiedButton';




const STEPS = [
  { id: 1, title: '<PERSON>', description: 'Podstawowe informacje' },
  { id: 2, title: 'Preferencje', description: 'Doświadczenie i wymagania' },
  { id: 3, title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', description: 'Zadatek i potwierdzenie' }
];

  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Step 1: Personal data
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    birthDate: '',
    emergencyContact: '',
    emergencyPhone: '',
    
    // Step 2: Preferences
    yogaExperience: '',
    dietaryRestrictions: '',
    medicalConditions: '',
    roomPreference: 'shared',
    specialRequests: '',
    
    // Step 3: Payment
    paymentMethod: 'transfer',
    agreeTerms: false,
    agreeNewsletter: false
  });
  
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateStep = (step) => {
    const newErrors = {};
    
    if (step === 1) {
      if (!formData.firstName.trim()) newErrors.firstName = 'Imię jest wymagane';
      if (!formData.lastName.trim()) newErrors.lastName = 'Nazwisko jest wymagane';
      if (!formData.email.trim()) newErrors.email = 'Email jest wymagany';
      if (!formData.email.includes('@')) newErrors.email = 'Nieprawidłowy email';
      if (!formData.phone.trim()) newErrors.phone = 'Telefon jest wymagany';
      if (!formData.emergencyContact.trim()) newErrors.emergencyContact = 'Kontakt awaryjny jest wymagany';
      if (!formData.emergencyPhone.trim()) newErrors.emergencyPhone = 'Telefon awaryjny jest wymagany';
    }
    
    if (step === 2) {
      if (!formData.yogaExperience) newErrors.yogaExperience = 'Wybierz poziom doświadczenia';
    }
    
    if (step === 3) {
      if (!formData.agreeTerms) newErrors.agreeTerms = 'Musisz zaakceptować regulamin';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 3));
    }
  };

  const handlePrev = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(3)) return;
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/booking', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          retreat: retreat,
          formData: formData,
          timestamp: new Date().toISOString()
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        onSuccess?.(result);
      } else {
        throw new Error(result.error || 'Błąd rezerwacji');
      }
    } catch (error) {
      console.error('Booking error:', error);
      setErrors({ submit: 'Wystąpił błąd. Spróbuj ponownie lub skontaktuj się z nami.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStep1 = () => (
    <div className="space-y-md">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-sm">
        <div>
          <label className="block text-sm font-medium text-enterprise-brown mb-2">
            Imię *
          </label>
          <input
            type="text"
            value={formData.firstName}
            onChange={(e) => updateFormData('firstName', e.target.value)}
            className={`w-full px-container-sm py-3 border rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold/30 ${
              errors.firstName ? 'border-charcoal-gold' : 'border-stone-light'
            }`}
            placeholder="Twoje imię"
          />
          {errors.firstName && <p className="text-charcoal-gold text-sm mt-1">{errors.firstName}</p>}
        </div>
        
        <div>
          <label className="block text-sm font-medium text-enterprise-brown mb-2">
            Nazwisko *
          </label>
          <input
            type="text"
            value={formData.lastName}
            onChange={(e) => updateFormData('lastName', e.target.value)}
            className={`w-full px-container-sm py-3 border rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30 ${
              errors.lastName ? 'border-charcoal-gold' : 'border-enterprise-brown/20'
            }`}
            placeholder="Twoje nazwisko"
          />
          {errors.lastName && <p className="text-charcoal-gold text-sm mt-1">{errors.lastName}</p>}
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-sm">
        <div>
          <label className="block text-sm font-medium text-enterprise-brown mb-2">
            Email *
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => updateFormData('email', e.target.value)}
            className={`w-full px-container-sm py-3 border rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30 ${
              errors.email ? 'border-charcoal-gold' : 'border-enterprise-brown/20'
            }`}
            placeholder="<EMAIL>"
          />
          {errors.email && <p className="text-charcoal-gold text-sm mt-1">{errors.email}</p>}
        </div>
        
        <div>
          <label className="block text-sm font-medium text-enterprise-brown mb-2">
            Telefon *
          </label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => updateFormData('phone', e.target.value)}
            className={`w-full px-container-sm py-3 border rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30 ${
              errors.phone ? 'border-charcoal-gold' : 'border-enterprise-brown/20'
            }`}
            placeholder="+48 123 456 789"
          />
          {errors.phone && <p className="text-charcoal-gold text-sm mt-1">{errors.phone}</p>}
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-enterprise-brown mb-2">
          Data urodzenia
        </label>
        <input
          type="date"
          value={formData.birthDate}
          onChange={(e) => updateFormData('birthDate', e.target.value)}
          className="w-full px-container-sm py-3 border border-enterprise-brown/20 rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30"
        />
      </div>
      
      <div className="bg-amber-50 p-4 rectangular">
        <h4 className="font-medium text-enterprise-brown mb-3">Kontakt awaryjny</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-sm">
          <div>
            <label className="block text-sm font-medium text-enterprise-brown mb-2">
              Imię i nazwisko *
            </label>
            <input
              type="text"
              value={formData.emergencyContact}
              onChange={(e) => updateFormData('emergencyContact', e.target.value)}
              className={`w-full px-container-sm py-3 border rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30 ${
                errors.emergencyContact ? 'border-charcoal-gold' : 'border-enterprise-brown/20'
              }`}
              placeholder="Imię i nazwisko"
            />
            {errors.emergencyContact && <p className="text-charcoal-gold text-sm mt-1">{errors.emergencyContact}</p>}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-enterprise-brown mb-2">
              Telefon *
            </label>
            <input
              type="tel"
              value={formData.emergencyPhone}
              onChange={(e) => updateFormData('emergencyPhone', e.target.value)}
              className={`w-full px-container-sm py-3 border rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30 ${
                errors.emergencyPhone ? 'border-charcoal-gold' : 'border-enterprise-brown/20'
              }`}
              placeholder="+48 123 456 789"
            />
            {errors.emergencyPhone && <p className="text-charcoal-gold text-sm mt-1">{errors.emergencyPhone}</p>}
          </div>
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-md">
      <div>
        <label className="block text-sm font-medium text-enterprise-brown mb-2">
          Doświadczenie w jodze *
        </label>
        <select
          value={formData.yogaExperience}
          onChange={(e) => updateFormData('yogaExperience', e.target.value)}
          className={`w-full px-container-sm py-3 border rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30 ${
            errors.yogaExperience ? 'border-charcoal-gold' : 'border-enterprise-brown/20'
          }`}
        >
          <option value="">Wybierz poziom</option>
          <option value="beginner">Początkujący (0-1 rok)</option>
          <option value="intermediate">Średniozaawansowany (1-3 lata)</option>
          <option value="advanced">Zaawansowany (3+ lata)</option>
          <option value="teacher">Instruktor jogi</option>
        </select>
        {errors.yogaExperience && <p className="text-charcoal-gold text-sm mt-1">{errors.yogaExperience}</p>}
      </div>
      
      <div>
        <label className="block text-sm font-medium text-enterprise-brown mb-2">
          Preferencje pokoju
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="roomPreference"
              value="shared"
              checked={formData.roomPreference === 'shared'}
              onChange={(e) => updateFormData('roomPreference', e.target.value)}
              className="mr-2"
            />
            Pokój dzielony (w cenie)
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="roomPreference"
              value="single"
              checked={formData.roomPreference === 'single'}
              onChange={(e) => updateFormData('roomPreference', e.target.value)}
              className="mr-2"
            />
            Pokój jednoosobowy (+500 PLN)
          </label>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-enterprise-brown mb-2">
          Ograniczenia dietetyczne
        </label>
        <textarea
          value={formData.dietaryRestrictions}
          onChange={(e) => updateFormData('dietaryRestrictions', e.target.value)}
          className="w-full px-container-sm py-3 border border-enterprise-brown/20 rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30"
          rows="3"
          placeholder="Wegetarianizm, alergie, nietolerancje..."
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-enterprise-brown mb-2">
          Problemy zdrowotne / kontuzje
        </label>
        <textarea
          value={formData.medicalConditions}
          onChange={(e) => updateFormData('medicalConditions', e.target.value)}
          className="w-full px-container-sm py-3 border border-enterprise-brown/20 rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30"
          rows="3"
          placeholder="Kontuzje, ograniczenia, leki..."
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-enterprise-brown mb-2">
          Dodatkowe uwagi
        </label>
        <textarea
          value={formData.specialRequests}
          onChange={(e) => updateFormData('specialRequests', e.target.value)}
          className="w-full px-container-sm py-3 border border-enterprise-brown/20 rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold/30"
          rows="3"
          placeholder="Szczególne prośby, oczekiwania..."
        />
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-md">
      <div className="bg-enterprise-brown/5 p-6 rectangular">
        <h4 className="font-medium text-enterprise-brown mb-sm">Podsumowanie rezerwacji</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Retreat:</span>
            <span className="font-medium">{retreat?.title}</span>
          </div>
          <div className="flex justify-between">
            <span>Cena podstawowa:</span>
            <span>{retreat?.price} PLN</span>
          </div>
          {formData.roomPreference === 'single' && (
            <div className="flex justify-between">
              <span>Pokój jednoosobowy:</span>
              <span>+500 PLN</span>
            </div>
          )}
          <hr className="my-2" />
          <div className="flex justify-between font-bold">
            <span>Razem:</span>
            <span>{(retreat?.price || 0) + (formData.roomPreference === 'single' ? 500 : 0)} PLN</span>
          </div>
          <div className="flex justify-between text-enterprise-brown">
            <span>Zadatek (30%):</span>
            <span className="font-bold">
              {Math.round(((retreat?.price || 0) + (formData.roomPreference === 'single' ? 500 : 0)) * 0.3)} PLN
            </span>
          </div>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-enterprise-brown mb-2">
          Sposób płatności zadatku
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="paymentMethod"
              value="transfer"
              checked={formData.paymentMethod === 'transfer'}
              onChange={(e) => updateFormData('paymentMethod', e.target.value)}
              className="mr-2"
            />
            Przelew bankowy (dane otrzymasz w emailu)
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="paymentMethod"
              value="blik"
              checked={formData.paymentMethod === 'blik'}
              onChange={(e) => updateFormData('paymentMethod', e.target.value)}
              className="mr-2"
            />
            BLIK (link otrzymasz w emailu)
          </label>
        </div>
      </div>
      
      <div className="space-y-3">
        <label className="flex items-start">
          <input
            type="checkbox"
            checked={formData.agreeTerms}
            onChange={(e) => updateFormData('agreeTerms', e.target.checked)}
            className="mr-2 mt-1"
          />
          <span className="text-sm">
            Akceptuję{' '}
            <a href="/regulamin" target="_blank" className="text-enterprise-brown underline">
              regulamin
            </a>{' '}
            i{' '}
            <a href="/polityka-prywatnosci" target="_blank" className="text-enterprise-brown underline">
              politykę prywatności
            </a>
            *
          </span>
        </label>
        {errors.agreeTerms && <p className="text-charcoal-gold text-sm">{errors.agreeTerms}</p>}
        
        <label className="flex items-start">
          <input
            type="checkbox"
            checked={formData.agreeNewsletter}
            onChange={(e) => updateFormData('agreeNewsletter', e.target.checked)}
            className="mr-2 mt-1"
          />
          <span className="text-sm">
            Chcę otrzymywać newsletter z informacjami o retreatach
          </span>
        </label>
      </div>
      
      {errors.submit && (
        <div className="bg-charcoal-gold/5 border border-charcoal-gold/20 rectangular p-4">
          <p className="text-charcoal-gold text-sm">{errors.submit}</p>
        </div>
      )}
    </div>
  );

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, y: 50 }}
          animate={{ scale: 1, y: 0 }}
          exit={{ scale: 0.9, y: 50 }}
          className="bg-white rectangular p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="text-center mb-lg">
            <h2 className="text-2xl font-cormorant text-enterprise-brown mb-2 /* TODO: Replace with SectionTitle */" /* TODO: Replace with SectionTitle */>
              Rezerwacja Retreatu
            </h2>
            <p className="text-charcoal-light">{retreat?.title}</p>
          </div>

          {/* Progress Steps */}
          <div className="flex justify-between mb-lg">
            {STEPS.map((step) => (
              <div
                key={step.id}
                className={`flex-1 text-center ${
                  step.id <= currentStep ? 'text-enterprise-brown' : 'text-gray-400'
                }`}
              >
                <div
                  className={`w-8 h-8 rectangular mx-auto mb-2 flex items-center justify-center text-sm font-medium ${
                    step.id <= currentStep
                      ? 'bg-enterprise-brown text-white'
                      : 'bg-gray-200 text-gray-400'
                  }`}
                >
                  {step.id}
                </div>
                <div className="text-xs font-medium">{step.title}</div>
                <div className="text-xs opacity-60">{step.description}</div>
              </div>
            ))}
          </div>

          {/* Form Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {currentStep === 1 && renderStep1()}
              {currentStep === 2 && renderStep2()}
              {currentStep === 3 && renderStep3()}
            </motion.div>
          </AnimatePresence>

          {/* Navigation */}
          <div className="flex justify-between pt-8 mt-lg border-t">
            <button
              onClick={currentStep === 1 ? onClose : handlePrev}
              className="btn-unified-secondary"
            >
              {currentStep === 1 ? 'Anuluj' : 'Wstecz'}
            </button>
            
            {currentStep < 3 ? (
              <button onClick={handleNext} className="btn-unified-primary">
                Dalej
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="btn-unified-primary"
              >
                {isSubmitting ? 'Wysyłanie...' : 'Zarezerwuj'}
              </button>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}


export default function BookingForm({ retreat, onClose, onSuccess }) {