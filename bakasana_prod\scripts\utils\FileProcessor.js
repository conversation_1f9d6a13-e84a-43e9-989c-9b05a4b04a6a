/**
 * Shared File Processing Utility
 * Eliminates code duplication across build scripts
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class FileProcessor {
  constructor(projectRoot = process.cwd()) {
    this.projectRoot = projectRoot;
    this.srcPath = path.join(projectRoot, 'src');
    this.publicDir = path.join(projectRoot, 'public');
    this.stats = {
      totalFiles: 0,
      processedFiles: 0,
      errors: 0
    };
  }

  /**
   * Get all files with specified extensions
   */
  getAllFiles(directory, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
    const patterns = extensions.map(ext => 
      path.join(directory, `**/*${ext}`).replace(/\\/g, '/')
    );
    
    let allFiles = [];
    patterns.forEach(pattern => {
      const files = glob.sync(pattern, { 
        ignore: ['**/node_modules/**', '**/.next/**', '**/dist/**'] 
      });
      allFiles = allFiles.concat(files);
    });
    
    return [...new Set(allFiles)]; // Remove duplicates
  }

  /**
   * Get all image files
   */
  getAllImages(directory) {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.avif', '.svg'];
    return this.getAllFiles(directory, imageExtensions);
  }

  /**
   * Get all CSS files
   */
  getAllCSS(directory) {
    const cssExtensions = ['.css', '.scss', '.sass'];
    return this.getAllFiles(directory, cssExtensions);
  }

  /**
   * Process files with a callback function
   */
  async processFiles(files, processor, options = {}) {
    const { 
      parallel = false, 
      batchSize = 10,
      onProgress = null,
      onError = null 
    } = options;

    this.stats.totalFiles = files.length;
    this.stats.processedFiles = 0;
    this.stats.errors = 0;

    if (parallel) {
      return this.processFilesParallel(files, processor, batchSize, onProgress, onError);
    } else {
      return this.processFilesSequential(files, processor, onProgress, onError);
    }
  }

  /**
   * Process files sequentially
   */
  async processFilesSequential(files, processor, onProgress, onError) {
    const results = [];

    for (const file of files) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const result = await processor(file, content);
        results.push({ file, result, success: true });
        this.stats.processedFiles++;
        
        if (onProgress) {
          onProgress(this.stats.processedFiles, this.stats.totalFiles, file);
        }
      } catch (error) {
        this.stats.errors++;
        const errorResult = { file, error: error.message, success: false };
        results.push(errorResult);
        
        if (onError) {
          onError(error, file);
        }
      }
    }

    return results;
  }

  /**
   * Process files in parallel batches
   */
  async processFilesParallel(files, processor, batchSize, onProgress, onError) {
    const results = [];
    
    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (file) => {
        try {
          const content = fs.readFileSync(file, 'utf8');
          const result = await processor(file, content);
          this.stats.processedFiles++;
          
          if (onProgress) {
            onProgress(this.stats.processedFiles, this.stats.totalFiles, file);
          }
          
          return { file, result, success: true };
        } catch (error) {
          this.stats.errors++;
          const errorResult = { file, error: error.message, success: false };
          
          if (onError) {
            onError(error, file);
          }
          
          return errorResult;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * Analyze file content
   */
  analyzeFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const stats = fs.statSync(filePath);
      
      return {
        path: filePath,
        size: stats.size,
        lines: content.split('\n').length,
        characters: content.length,
        lastModified: stats.mtime,
        extension: path.extname(filePath),
        content: content
      };
    } catch (error) {
      return {
        path: filePath,
        error: error.message
      };
    }
  }

  /**
   * Safe file write with backup
   */
  safeWriteFile(filePath, content, createBackup = true) {
    try {
      if (createBackup && fs.existsSync(filePath)) {
        const backupPath = `${filePath}.backup.${Date.now()}`;
        fs.copyFileSync(filePath, backupPath);
      }
      
      fs.writeFileSync(filePath, content, 'utf8');
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Get processing statistics
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalFiles > 0 
        ? ((this.stats.processedFiles - this.stats.errors) / this.stats.totalFiles * 100).toFixed(2)
        : 0
    };
  }

  /**
   * Reset statistics
   */
  resetStats() {
    this.stats = {
      totalFiles: 0,
      processedFiles: 0,
      errors: 0
    };
  }
}

module.exports = FileProcessor;
