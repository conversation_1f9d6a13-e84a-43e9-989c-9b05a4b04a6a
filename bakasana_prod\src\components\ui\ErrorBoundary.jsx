
'use client';
import React from 'react';

import { motion  } from 'framer-motion';

import { HeroTitle, BodyText  } from '@/components/ui/UnifiedTypography';
import { UnifiedButton  } from '@/components/ui/UnifiedButton';

export function EmptyState({ title, description, action, icon }) {
export function LoadingState({ type = 'default' }) {
export function NetworkError({ onRetry }) {
export function OfflineState() {



class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      retryCount: 0 
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error to monitoring service
    if (typeof window !== 'undefined') {
      console.error('Error Boundary caught an error:', error, errorInfo);
      
      // Send to error tracking service
      if (window.gtag) {
        window.gtag('event', 'exception', {
          description: error.toString(),
          fatal: false
        });
      }
    }
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  render() {
    if (this.state.hasError) {
      return (
        <ErrorFallback
          error={this.state.error}
          onRetry={this.handleRetry}
          retryCount={this.state.retryCount}
          fallback={this.props.fallback}
        />
      );
    }

    return this.props.children;
  }
}

// Premium error fallback component
function ErrorFallback({ error, onRetry, retryCount, fallback }) {
  if (fallback) {
    return fallback;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="min-h-[400px] flex items-center justify-center p-8"
    >
      <div className="unified-card max-w-md w-full text-center p-8">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring" }}
          className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-sunset/20 to-terra/20 rounded-full flex items-center justify-center"
        >
          <svg className="w-8 h-8 text-terra" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </motion.div>

        <HeroTitle level={3} className="mb-4">
          Coś poszło nie tak
        </HeroTitle>

        <BodyText className="text-charcoal-light mb-6">
          Przepraszamy za niedogodności. Wystąpił nieoczekiwany błąd, ale już nad tym pracujemy.
        </BodyText>

        <div className="space-y-4">
          <UnifiedButton
            onClick={onRetry}
            variant="primary"
            className="w-full"
            disabled={retryCount >= 3}
          >
            {retryCount >= 3 ? 'Zbyt wiele prób' : 'Spróbuj ponownie'}
          </UnifiedButton>

          <UnifiedButton
            onClick={() => window.location.href = '/'}
            variant="secondary"
            className="w-full"
          >
            Wróć do strony głównej
          </UnifiedButton>
        </div>

        {process.env.NODE_ENV === 'development' && error && (
          <details className="mt-6 text-left">
            <summary className="cursor-pointer text-sm text-charcoal-light">
              Szczegóły błędu (dev)
            </summary>
            <pre className="mt-2 text-xs bg-charcoal/5 p-3 rounded overflow-auto">
              {error.toString()}
            </pre>
          </details>
        )}
      </div>
    </motion.div>
  );
}

// Specialized error states
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="text-center p-8"
    >
      <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-sunset/20 to-terra/20 rounded-full flex items-center justify-center">
        <svg className="w-8 h-8 text-terra" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
        </svg>
      </div>
      <HeroTitle level={4} className="mb-2">Brak połączenia</HeroTitle>
      <BodyText className="text-charcoal-light mb-4">
        Sprawdź połączenie internetowe i spróbuj ponownie.
      </BodyText>
      <UnifiedButton onClick={onRetry} variant="primary">
        Spróbuj ponownie
      </UnifiedButton>
    </motion.div>
  );
}

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center p-12"
    >
      <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-sand-light/30 to-sand-light/50 rounded-full flex items-center justify-center">
        {icon || (
          <svg className="w-10 h-10 text-stone" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
        )}
      </div>
      
      <HeroTitle level={3} className="mb-3">
        {title || 'Brak danych'}
      </HeroTitle>
      
      <BodyText className="text-charcoal-light mb-6 max-w-md mx-auto">
        {description || 'Nie znaleźliśmy żadnych elementów do wyświetlenia.'}
      </BodyText>
      
      {action}
    </motion.div>
  );
}

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-80 bg-charcoal text-silk p-4 rounded-lg shadow-lg z-50"
    >
      <div className="flex items-center gap-3">
        <div className="w-2 h-2 bg-sunset rounded-full animate-pulse"></div>
        <div>
          <p className="font-medium text-sm">Tryb offline</p>
          <p className="text-xs text-silk/80">Niektóre funkcje mogą być niedostępne</p>
        </div>
      </div>
    </motion.div>
  );
}

// Loading state with skeleton
  const skeletonLines = {
    default: 3,
    card: 5,
    list: 8,
    profile: 2
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="space-y-4 p-6"
    >
      {Array.from({ length: skeletonLines[type] }).map((_, i) => (
        <div
          key={i}
          className="h-4 bg-gradient-to-r from-sand-light/20 via-sand-light/40 to-sand-light/20 bg-[length:200%_100%] animate-shimmer rounded"
          style={{ width: `${Math.random() * 40 + 60}%` }}
        />
      ))}
    </motion.div>
  );
}



export default ErrorBoundary;