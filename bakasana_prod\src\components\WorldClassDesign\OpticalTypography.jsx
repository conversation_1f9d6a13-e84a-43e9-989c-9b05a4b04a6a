'use client';

import React from 'react';

/**
 * 🎯 OPTICAL TYPOGRAPHY - TOP 1% DESIGN FEATURE
 * Perfekcyjne wyrównanie optyczne, manual kerning, hanging punctuation
 * Inspirowane przez Apple, Kinfolk, najle<PERSON>ze editorial design
 */
const OpticalTypography = ({ 
  children, 
  as: Component = 'div',
  variant = 'body',
  opticalAlign = false,
  manualKerning = false,
  hangingPunctuation = false,
  preventOrphans = false,
  className = '',
  ...props 
}) => {
  
  // Optical alignment - ujemny margin-left dla idealnego wyrównania
  const getOpticalAlignmentStyle = () => {
    if (!opticalAlign) return {};
    
    const firstChar = typeof children === 'string' ? children.charAt(0) : '';
    const opticalAdjustments = {
      'A': { marginLeft: '-0.02em' },
      'V': { marginLeft: '-0.02em' },
      'W': { marginLeft: '-0.02em' },
      'T': { marginLeft: '-0.01em' },
      'Y': { marginLeft: '-0.01em' },
      'L': { marginLeft: '-0.005em' },
      'P': { marginLeft: '-0.005em' },
      'F': { marginLeft: '-0.005em' },
      '"': { marginLeft: '-0.2em' },
      "'": { marginLeft: '-0.1em' },
      '—': { marginLeft: '-0.1em' },
      '–': { marginLeft: '-0.05em' },
    };
    
    return opticalAdjustments[firstChar] || {};
  };

  // Manual kerning dla problematycznych par liter
  const applyManualKerning = (text) => {
    if (!manualKerning || typeof text !== 'string') return text;
    
    const kerningPairs = {
      'AV': 'A<span style="margin-left: -0.05em">V</span>',
      'AW': 'A<span style="margin-left: -0.05em">W</span>',
      'WA': 'W<span style="margin-left: -0.03em">A</span>',
      'Wa': 'W<span style="margin-left: -0.03em">a</span>',
      'To': 'T<span style="margin-left: -0.08em">o</span>',
      'Ta': 'T<span style="margin-left: -0.06em">a</span>',
      'Te': 'T<span style="margin-left: -0.06em">e</span>',
      'Yo': 'Y<span style="margin-left: -0.08em">o</span>',
      'Ya': 'Y<span style="margin-left: -0.06em">a</span>',
      'Ye': 'Y<span style="margin-left: -0.06em">e</span>',
      'LT': 'L<span style="margin-left: -0.06em">T</span>',
      'LV': 'L<span style="margin-left: -0.08em">V</span>',
      'LW': 'L<span style="margin-left: -0.08em">W</span>',
      'LY': 'L<span style="margin-left: -0.08em">Y</span>',
      'PA': 'P<span style="margin-left: -0.06em">A</span>',
      'VA': 'V<span style="margin-left: -0.08em">A</span>',
      'r.': 'r<span style="margin-left: -0.03em">.</span>',
      'f.': 'f<span style="margin-left: -0.03em">.</span>',
    };
    
    let result = text;
    Object.entries(kerningPairs).forEach(([pair, replacement]) => {
      result = result.replace(new RegExp(pair, 'g'), replacement);
    });
    
    return result;
  };

  // Prevent orphans - dodaj &nbsp; między ostatnimi słowami
  const preventOrphansText = (text) => {
    if (!preventOrphans || typeof text !== 'string') return text;
    
    const words = text.split(' ');
    if (words.length < 3) return text;
    
    // Połącz ostatnie 2-3 słowa z &nbsp;
    const lastWords = words.slice(-2);
    const firstWords = words.slice(0, -2);
    
    return [...firstWords, lastWords.join('\u00A0')].join(' ');
  };

  // Smart quotes - zamień " " na „ "
  const applySmartQuotes = (text) => {
    if (typeof text !== 'string') return text;
    
    // Polish smart quotes
    return text
      .replace(/"/g, '\u201E')
      .replace(/\u201E([^\u201E\u201D]*?)\u201E/g, '\u201E$1\u201D')
      .replace(/'/g, '\u2019')
      .replace(/\u2019([^\u2018\u2019]*?)\u2019/g, '\u2018$1\u2019');
  };

  // Variant styles
  const variantStyles = {
    h1: {
      fontSize: 'clamp(3rem, 6vw, 4.5rem)',
      fontWeight: 200,
      letterSpacing: '-0.02em',
      lineHeight: 1.1,
      fontFamily: 'var(--font-cormorant)',
    },
    h2: {
      fontSize: 'clamp(2.25rem, 4vw, 3rem)',
      fontWeight: 300,
      letterSpacing: '-0.015em',
      lineHeight: 1.2,
      fontFamily: 'var(--font-cormorant)',
    },
    h3: {
      fontSize: 'clamp(1.5rem, 3vw, 2rem)',
      fontWeight: 400,
      letterSpacing: '-0.01em',
      lineHeight: 1.3,
      fontFamily: 'var(--font-cormorant)',
    },
    body: {
      fontSize: 'clamp(1rem, 1.5vw, 1.125rem)',
      fontWeight: 300,
      letterSpacing: '0.01em',
      lineHeight: 1.8,
      fontFamily: 'var(--font-sans)',
    },
    caption: {
      fontSize: '0.875rem',
      fontWeight: 300,
      letterSpacing: '0.05em',
      lineHeight: 1.6,
      fontFamily: 'var(--font-sans)',
    },
    display: {
      fontSize: 'clamp(4rem, 8vw, 6rem)',
      fontWeight: 100,
      letterSpacing: '-0.03em',
      lineHeight: 1.0,
      fontFamily: 'var(--font-cormorant)',
    }
  };

  // Combine all styles
  const combinedStyle = {
    ...variantStyles[variant],
    ...getOpticalAlignmentStyle(),
    ...(hangingPunctuation && { textIndent: '-0.2em', paddingLeft: '0.2em' }),
    textRendering: 'optimizeLegibility',
    fontFeatureSettings: '"liga" 1, "kern" 1',
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
  };

  // Process text content
  const processedText = () => {
    let text = children;
    
    if (typeof text === 'string') {
      text = applySmartQuotes(text);
      text = preventOrphansText(text);
      text = applyManualKerning(text);
      
      if (manualKerning) {
        return <span dangerouslySetInnerHTML={{ __html: text }} />;
      }
    }
    
    return text;
  };

  return (
    <Component
      className={`optical-typography ${className}`}
      style={combinedStyle}
      {...props}
    >
      {processedText()}
    </Component>
  );
};

export default React.memo(OpticalTypography);