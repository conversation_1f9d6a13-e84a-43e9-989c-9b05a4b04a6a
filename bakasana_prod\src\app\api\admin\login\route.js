
import { NextResponse  } from 'next/server';

import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';

export async function POST(request) {

const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD;
const JWT_SECRET = process.env.JWT_SECRET;

// Validate required environment variables
if (!ADMIN_PASSWORD || !JWT_SECRET) {
  throw new Error('Missing required environment variables: ADMIN_PASSWORD and JWT_SECRET must be set');
}

// Rate limiting - proste w pamięci (w produkcji użyj Redis)
const loginAttempts = new Map();
const MAX_ATTEMPTS = 5;
const LOCKOUT_TIME = 15 * 60 * 1000; // 15 minut

function getClientIP(request) {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

function isRateLimited(ip) {
  const attempts = loginAttempts.get(ip);
  
  if (!attempts) {
    return false;
  }
  
  // Sprawdź czy lockout wygasł
  if (Date.now() - attempts.lastAttempt > LOCKOUT_TIME) {
    loginAttempts.delete(ip);
    return false;
  }
  
  return attempts.count >= MAX_ATTEMPTS;
}

function recordFailedAttempt(ip) {
  const attempts = loginAttempts.get(ip) || { count: 0, lastAttempt: 0 };
  
  attempts.count += 1;
  attempts.lastAttempt = Date.now();
  
  loginAttempts.set(ip, attempts);
}

function clearFailedAttempts(ip) {
  loginAttempts.delete(ip);
}

  try {
    const clientIP = getClientIP(request);
    
    // Sprawdź rate limiting
    if (isRateLimited(clientIP)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Zbyt wiele nieudanych prób logowania. Spróbuj ponownie za 15 minut.',
          lockoutTime: LOCKOUT_TIME / 1000 / 60 // w minutach
        },
        { status: 429 }
      );
    }

    const { password } = await request.json();

    // Walidacja hasła
    if (!password) {
      recordFailedAttempt(clientIP);
      return NextResponse.json(
        { success: false, error: 'Hasło jest wymagane' },
        { status: 400 }
      );
    }

    // Verify password using bcrypt
    const isValidPassword = await verifyPassword(password, ADMIN_PASSWORD);
    
    if (!isValidPassword) {
      recordFailedAttempt(clientIP);
      
      // Log nieudanej próby logowania
      console.warn(`Failed admin login attempt from IP: ${clientIP} at ${new Date().toISOString()}`);
      
      return NextResponse.json(
        { success: false, error: 'Nieprawidłowe hasło' },
        { status: 401 }
      );
    }

    // Wyczyść nieudane próby po udanym logowaniu
    clearFailedAttempts(clientIP);

    // Generuj JWT token
    const token = jwt.sign(
      { 
        role: 'admin',
        ip: clientIP,
        loginTime: Date.now()
      },
      JWT_SECRET,
      { 
        expiresIn: '24h',
        issuer: 'bakasana-travel-admin',
        audience: 'bakasana-travel-app'
      }
    );

    // Log udanego logowania


    return NextResponse.json({
      success: true,
      token,
      expiresIn: 24 * 60 * 60, // 24 godziny w sekundach
      message: 'Zalogowano pomyślnie'
    });

  } catch (error) {
    console.error('Admin login error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Błąd serwera podczas logowania',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Secure password verification using bcrypt
async function verifyPassword(inputPassword, hashedPassword) {
  try {
    return await bcrypt.compare(inputPassword, hashedPassword);
  } catch (error) {
    console.error('Password verification error:', error);
    return false;
  }
}

// Hash password utility (for initial setup)
async function hashPassword(password) {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

// GET endpoint dla sprawdzenia statusu
export async function GET() {
  return NextResponse.json({
    message: 'Admin login endpoint is working',
    timestamp: new Date().toISOString(),
    rateLimit: {
      maxAttempts: MAX_ATTEMPTS,
      lockoutTimeMinutes: LOCKOUT_TIME / 1000 / 60
    }
  });
}
