import { useState, useMemo, useEffect  } from 'react';

import { motion, AnimatePresence  } from 'framer-motion';

import { HeroTitle, SectionTitle, CardTitle, BodyText  } from '@/components/ui/UnifiedTypography';
import UnifiedButton from '@/components/ui/UnifiedButton';

import { ScrollReveal  } from './ScrollReveal';

export default function BookingCalendar() {

'use client';




// Dynamic imports to avoid SSR issues
let Calendar, momentLocalizer, moment;

if (typeof window !== 'undefined') {
  try {
    const BigCalendar = require('react-big-calendar');
    Calendar = BigCalendar.Calendar;
    momentLocalizer = BigCalendar.momentLocalizer;
    moment = require('moment');
    require('moment/locale/pl');
    require('react-big-calendar/lib/css/react-big-calendar.css');
    moment.locale('pl');
  } catch (error) {
    console.warn('Calendar libraries not loaded:', error);
  }
}

// Mock retreat data - replace with real data from CMS/API
const retreatEvents = [
  {
    id: 1,
    title: 'Bali Yoga Retreat',
    start: new Date(2025, 2, 15), // March 15, 2025
    end: new Date(2025, 2, 22),   // March 22, 2025
    price: 2900,
    spotsLeft: 3,
    status: 'available',
    description: 'Wiosenny retreat jogowy na Bali z praktyką na plażach Uluwatu',
    includes: ['7 nocy w hotelu', 'Wszystkie posiłki', 'Transport', 'Joga 2x dziennie', 'Zwiedzanie']
  },
  {
    id: 2,
    title: 'Bali Yoga Retreat',
    start: new Date(2025, 4, 10), // May 10, 2025
    end: new Date(2025, 4, 17),   // May 17, 2025
    price: 3200,
    spotsLeft: 8,
    status: 'available',
    description: 'Majowy retreat z fokusem na advanced asany i medytację',
    includes: ['7 nocy w hotelu', 'Wszystkie posiłki', 'Transport', 'Joga 2x dziennie', 'Warsztaty']
  },
  {
    id: 3,
    title: 'Bali Yoga Retreat',
    start: new Date(2025, 6, 5),  // July 5, 2025
    end: new Date(2025, 6, 12),   // July 12, 2025
    price: 3400,
    spotsLeft: 0,
    status: 'full',
    description: 'Letni retreat - WYPRZEDANY',
    includes: ['7 nocy w hotelu', 'Wszystkie posiłki', 'Transport', 'Joga 2x dziennie']
  },
  {
    id: 4,
    title: 'Sri Lanka Yoga Retreat',
    start: new Date(2025, 8, 20), // September 20, 2025
    end: new Date(2025, 8, 27),   // September 27, 2025
    price: 2700,
    spotsLeft: 12,
    status: 'available',
    description: 'Jesienny retreat na Sri Lance - Ella i Sigiriya',
    includes: ['7 nocy w hotelach', 'Wszystkie posiłki', 'Transport', 'Joga w górach', 'Safari']
  }
];

  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showBookingForm, setShowBookingForm] = useState(false);
  const [view, setView] = useState('month');
  const [isCalendarReady, setIsCalendarReady] = useState(false);

  // Initialize calendar after component mounts
  useEffect(() => {
    if (typeof window !== 'undefined' && Calendar && moment) {
      setIsCalendarReady(true);
    }
  }, []);

  // Event style function
  const eventStyleGetter = (event) => {
    let backgroundColor = 'var(--temple-gold)'; // temple color
    let borderColor = 'var(--temple-gold)';
    
    if (event.status === 'full') {
      backgroundColor = 'var(--stone)'; // gray
      borderColor = 'var(--stone)';
    } else if (event.spotsLeft <= 3) {
      backgroundColor = 'var(--temple-gold)'; // amber - almost full
      borderColor = 'var(--temple-gold)';
    }

    return {
      style: {
        backgroundColor,
        borderColor,
        color: 'white',
        border: 'none',
        fontSize: '12px',
        fontWeight: '500'
      }
    };
  };

  // Custom messages in Polish
  const messages = {
    allDay: 'Cały dzień',
    previous: 'Poprzedni',
    next: 'Następny',
    today: 'Dziś',
    month: 'Miesiąc',
    week: 'Tydzień',
    day: 'Dzień',
    agenda: 'Agenda',
    date: 'Data',
    time: 'Czas',
    event: 'Wydarzenie',
    noEventsInRange: 'Brak retreatów w tym okresie',
    showMore: total => `+ ${total} więcej`
  };

  const handleSelectEvent = (event) => {
    setSelectedEvent(event);
  };

  const handleBookNow = () => {
    setShowBookingForm(true);
  };

  return (
    <div className="space-y-lg">
      {/* Calendar Header */}
      <ScrollReveal className="text-center">
        <h2 className="text-3xl md:text-4xl font-cormorant text-enterprise-brown mb-sm /* TODO: Replace with HeroTitle */ /* TODO: Replace with SectionTitle */" /* TODO: Replace with SectionTitle */>
          Kalendarz Retreatów
        </h2>
        <p className="text-charcoal-light text-lg max-w-2xl mx-auto">
          Wybierz termin, który Ci odpowiada i zarezerwuj swoje miejsce na niezapomnianej przygodzie
        </p>
      </ScrollReveal>

      {/* Legend */}
      <div className="flex flex-wrap justify-center gap-sm text-sm">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-enterprise-brown"></div>
          <span>Dostępne miejsca</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-charcoal-gold"></div>
          <span>Ostatnie miejsca</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-gray-400"></div>
          <span>Wyprzedane</span>
        </div>
      </div>

      {/* Calendar */}
      <ScrollReveal className="bg-white rectangular shadow-soft p-6 overflow-hidden">
        <div style={{ height: '600px' }}>
          {isCalendarReady && Calendar && momentLocalizer ? (
            <Calendar
              localizer={momentLocalizer(moment)}
              events={retreatEvents}
              startAccessor="start"
              endAccessor="end"
              style={{ height: '100%' }}
              eventPropGetter={eventStyleGetter}
              messages={messages}
              views={['month', 'agenda']}
              view={view}
              onView={setView}
              onSelectEvent={handleSelectEvent}
              popup
              showMultiDayTimes
              step={60}
              showAllEvents
              className="custom-calendar"
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-4xl mb-sm">📅</div>
                <CardTitle>Kalendarz się ładuje...</CardTitle>
                <p className="text-charcoal-light">Proszę czekać</p>
              </div>
            </div>
          )}
        </div>
      </ScrollReveal>

      {/* Event Details Modal */}
      <AnimatePresence>
        {selectedEvent && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedEvent(null)}
          >
            <motion.div
              initial={{ scale: 0.9, y: 50 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 50 }}
              className="bg-white rectangular p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-md">
                <h3 className="text-2xl font-cormorant text-enterprise-brown mb-2 /* TODO: Replace with SectionTitle */" /* TODO: Replace with CardTitle */>
                  {selectedEvent.title}
                </h3>
                <p className="text-charcoal-light mb-sm">
                  {moment(selectedEvent.start).format('DD MMMM')} - {moment(selectedEvent.end).format('DD MMMM YYYY')}
                </p>
                <div className="flex justify-center items-center gap-sm mb-sm">
                  <span className="text-3xl font-bold text-enterprise-brown">
                    {selectedEvent.price} PLN
                  </span>
                  {selectedEvent.status === 'available' && (
                    <span className="bg-sanctuary text-enterprise-brown px-3 py-1 rectangular text-sm">
                      {selectedEvent.spotsLeft} miejsc dostępnych
                    </span>
                  )}
                  {selectedEvent.status === 'full' && (
                    <span className="bg-red-100 text-red-800 px-3 py-1 rectangular text-sm">
                      Wyprzedane
                    </span>
                  )}
                </div>
              </div>

              <div className="space-y-md">
                <div>
                  <h4 className="font-medium text-enterprise-brown mb-2">Opis retreatu:</h4>
                  <p className="text-charcoal-light">{selectedEvent.description}</p>
                </div>

                <div>
                  <h4 className="font-medium text-enterprise-brown mb-2">W cenie:</h4>
                  <ul className="space-y-1">
                    {selectedEvent.includes.map((item, index) => (
                      <li key={index} className="flex items-center gap-2 text-charcoal-light">
                        <span className="text-enterprise-brown">✓</span>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex flex-col sm:flex-row gap-sm pt-6">
                  {selectedEvent.status === 'available' ? (
                    <button
                      onClick={handleBookNow}
                      className="btn-unified-primary flex-1"
                    >
                      Zarezerwuj miejsce
                    </button>
                  ) : (
                    <button
                      disabled
                      className="btn-unified-primary opacity-50 cursor-not-allowed flex-1"
                    >
                      Wyprzedane
                    </button>
                  )}
                  <button
                    onClick={() => setSelectedEvent(null)}
                    className="btn-unified-secondary flex-1"
                  >
                    Zamknij
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Quick Stats */}
      <ScrollReveal className="grid grid-cols-1 md:grid-cols-3 gap-md">
        <div className="text-center p-6 bg-enterprise-brown/5 rectangular">
          <div className="text-2xl font-bold text-enterprise-brown mb-2">
            {retreatEvents.filter(e => e.status === 'available').length}
          </div>
          <div className="text-charcoal-light">Dostępne retreaty</div>
        </div>
        <div className="text-center p-6 bg-terra/5 rectangular">
          <div className="text-2xl font-bold text-enterprise-brown mb-2">
            {retreatEvents.reduce((sum, e) => sum + (e.status === 'available' ? e.spotsLeft : 0), 0)}
          </div>
          <div className="text-charcoal-light">Wolne miejsca</div>
        </div>
        <div className="text-center p-6 bg-sunset/5 rectangular">
          <div className="text-2xl font-bold text-enterprise-brown mb-2">7-8</div>
          <div className="text-charcoal-light">Dni retreatu</div>
        </div>
      </ScrollReveal>
    </div>
  );
}
