'use client';

import Link from 'next/link';
import React from 'react';

import PerformantWhatsApp from '../PerformantWhatsApp';

const Footer = React.memo(function Footer() {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    services: [
      { href: '/retreaty', label: 'Retreaty Jogi', description: 'Bali & Sri Lanka' },
      { href: '/zajecia-stacjonarne', label: 'Zajęcia Stacjonarne', description: 'Studio Warszawa' },
      { href: '/zajecia-online', label: 'Zajęcia Online', description: 'Joga w domu' },
      { href: '/rezerwacja', label: 'Rezerwacje', description: '<PERSON>arezerwuj miej<PERSON>' }
    ],
    destinations: [
      { href: '/retreaty-jogi-bali-2025', label: 'Retreaty Bali', description: 'Luksusowe wyjazdy' },
      { href: '/joga-sri-lanka-retreat', label: 'Retreaty Sri Lanka', description: 'Ducho<PERSON> podróż' },
      { href: '/transformacyjne-podroze-azja', label: 'Podróże Azja', description: 'Transformacja' }
    ],
    company: [
      { href: '/o-mnie', label: 'O Julii', description: 'Instruktor jogi' },
      { href: '/julia-jakubowicz-instruktor', label: 'Doświadczenie', description: 'Kwalifikacje' },
      { href: '/kontakt', label: 'Kontakt', description: 'Skontaktuj się' },
      { href: '/polityka-prywatnosci', label: 'Polityka Prywatności', description: 'RODO' }
    ]
  };

  return (
    <footer className="footer section-transition bg-gradient-to-t from-charcoal/5 to-transparent" role="contentinfo" aria-label="Informacje o stronie">
      <div className="max-w-7xl mx-auto px-container-sm py-section">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-lg mb-xl">
          
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="logo text-2xl font-cormorant text-enterprise-brown mb-md">
              BAKASANA
            </div>
            <p className="text-charcoal-light text-sm mb-md leading-relaxed">
              Luksusowe retreaty jogi na Bali i Sri Lanka. Transformacyjne podróże duchowe z doświadczonym instruktorem.
            </p>
            
            {/* Contact Info for SEO */}
            <div className="space-y-2 text-xs text-charcoal-light mb-md">
              <div className="flex items-center gap-2">
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
                <span>Studio: Warszawa, Polska</span>
              </div>
              <div className="flex items-center gap-2">
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                </svg>
                <span>+48 606 101 523</span>
              </div>
              <div className="flex items-center gap-2">
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
                <span><EMAIL></span>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex gap-3">
              <a
                href="https://www.instagram.com/fly_with_bakasana"
                target="_blank"
                rel="noopener noreferrer"
                className="social-icon micro-interaction focus-visible-only"
                aria-label="Odwiedź nasz profil na Instagram (otwiera w nowej karcie)"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </a>
              <PerformantWhatsApp 
                variant="icon" 
                className="social-icon micro-interaction focus-visible-only"
                message="Cześć! Interesuję się retreatami jogowymi na Bali i Sri Lanka."
              />
              <a
                href="mailto:<EMAIL>"
                className="social-icon micro-interaction focus-visible-only"
                aria-label="Wyślij email do nas"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Services Links */}
          <div>
            <h3 className="text-sm font-medium text-enterprise-brown mb-md">Nasze Usługi</h3>
            <nav className="space-y-3" role="navigation" aria-label="Usługi">
              {footerLinks.services.map((link, index) => (
                <Link
                  key={index}
                  href={link.href}
                  className="block group hover:text-enterprise-brown transition-colors duration-200"
                >
                  <div className="text-sm text-charcoal-light group-hover:text-enterprise-brown">
                    {link.label}
                  </div>
                  <div className="text-xs text-charcoal-light/60 group-hover:text-enterprise-brown/70">
                    {link.description}
                  </div>
                </Link>
              ))}
            </nav>
          </div>

          {/* Destinations Links */}
          <div>
            <h3 className="text-sm font-medium text-enterprise-brown mb-md">Destynacje</h3>
            <nav className="space-y-3" role="navigation" aria-label="Destynacje">
              {footerLinks.destinations.map((link, index) => (
                <Link
                  key={index}
                  href={link.href}
                  className="block group hover:text-enterprise-brown transition-colors duration-200"
                >
                  <div className="text-sm text-charcoal-light group-hover:text-enterprise-brown">
                    {link.label}
                  </div>
                  <div className="text-xs text-charcoal-light/60 group-hover:text-enterprise-brown/70">
                    {link.description}
                  </div>
                </Link>
              ))}
            </nav>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="text-sm font-medium text-enterprise-brown mb-md">Firma</h3>
            <nav className="space-y-3" role="navigation" aria-label="Informacje o firmie">
              {footerLinks.company.map((link, index) => (
                <Link
                  key={index}
                  href={link.href}
                  className="block group hover:text-enterprise-brown transition-colors duration-200"
                >
                  <div className="text-sm text-charcoal-light group-hover:text-enterprise-brown">
                    {link.label}
                  </div>
                  <div className="text-xs text-charcoal-light/60 group-hover:text-enterprise-brown/70">
                    {link.description}
                  </div>
                </Link>
              ))}
            </nav>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-charcoal-light/10 pt-lg">
          <div className="flex flex-col md:flex-row justify-between items-center gap-md">
            <div className="text-xs text-charcoal-light">
              © {currentYear} Bakasana Studio. Wszystkie prawa zastrzeżone. Made with ♡ for spiritual journeys.
            </div>
            
            {/* Additional SEO Links */}
            <div className="flex flex-wrap gap-4 text-xs">
              <Link href="/sitemap.xml" className="text-charcoal-light hover:text-enterprise-brown transition-colors">
                Mapa strony
              </Link>
              <Link href="/robots.txt" className="text-charcoal-light hover:text-enterprise-brown transition-colors">
                Robots.txt
              </Link>
              <span className="text-charcoal-light/40">|</span>
              <span className="text-charcoal-light/60">
                Retreaty jogi Bali • Zajęcia stacjonarne Warszawa • Joga online
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Schema.org Structured Data for Organization */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Bakasana Studio",
            "alternateName": "Fly with Bakasana",
            "url": "https://flywithbakasana.pl",
            "logo": "https://flywithbakasana.pl/og-image.jpg",
            "description": "Luksusowe retreaty jogi na Bali i Sri Lanka oraz zajęcia stacjonarne w Warszawie. Transformacyjne podróże duchowe z doświadczonym instruktorem Julią Jakubowicz.",
            "founder": {
              "@type": "Person",
              "name": "Julia Jakubowicz",
              "jobTitle": "Instruktor Jogi",
              "url": "https://flywithbakasana.pl/julia-jakubowicz-instruktor"
            },
            "contactPoint": [
              {
                "@type": "ContactPoint",
                "telephone": "+48-606-101-523",
                "contactType": "customer service",
                "availableLanguage": ["Polish", "English"],
                "areaServed": ["PL", "EU"]
              }
            ],
            "address": {
              "@type": "PostalAddress",
              "addressLocality": "Warszawa",
              "addressCountry": "PL"
            },
            "sameAs": [
              "https://www.instagram.com/fly_with_bakasana"
            ],
            "hasOfferCatalog": {
              "@type": "OfferCatalog",
              "name": "Usługi Bakasana Studio",
              "itemListElement": [
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Service",
                    "name": "Retreaty Jogi Bali",
                    "description": "Luksusowe retreaty jogowe na Bali",
                    "url": "https://flywithbakasana.pl/retreaty-jogi-bali-2025"
                  }
                },
                {
                  "@type": "Offer", 
                  "itemOffered": {
                    "@type": "Service",
                    "name": "Retreaty Jogi Sri Lanka",
                    "description": "Duchowe podróże jogowe na Sri Lance",
                    "url": "https://flywithbakasana.pl/joga-sri-lanka-retreat"
                  }
                },
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Service", 
                    "name": "Zajęcia Stacjonarne",
                    "description": "Zajęcia jogi w studio w Warszawie",
                    "url": "https://flywithbakasana.pl/zajecia-stacjonarne"
                  }
                },
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Service",
                    "name": "Zajęcia Online",
                    "description": "Joga online w domu",
                    "url": "https://flywithbakasana.pl/zajecia-online"
                  }
                }
              ]
            }
          })
        }}
      />
    </footer>
  );
});

export default Footer;
