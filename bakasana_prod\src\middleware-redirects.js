// SEO Redirects Configuration
// Dodaj tutaj 301 redirects dla starych URL-i

export const seoRedirects = [
  // Przykładowe redirecty - dodaj według potrzeb
  {
    source: '/zajecia',
    destination: '/zajecia-stacjonarne',
    permanent: true
  },
  {
    source: '/online',
    destination: '/zajecia-online', 
    permanent: true
  },
  {
    source: '/booking',
    destination: '/rezerwacja',
    permanent: true
  },
  {
    source: '/retreats',
    destination: '/retreaty',
    permanent: true
  },
  {
    source: '/bali',
    destination: '/retreaty-jogi-bali-2025',
    permanent: true
  },
  {
    source: '/sri-lanka',
    destination: '/joga-sri-lanka-retreat',
    permanent: true
  },
  {
    source: '/about',
    destination: '/o-mnie',
    permanent: true
  },
  {
    source: '/contact',
    destination: '/kontakt',
    permanent: true
  },
  {
    source: '/instructor',
    destination: '/juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor',
    permanent: true
  },
  {
    source: '/gallery',
    destination: '/galeria',
    permanent: true
  },
  {
    source: '/privacy',
    destination: '/polityka-prywatnosci',
    permanent: true
  }
];

// Funkcja do sprawdzania czy URL wymaga redirectu
export function checkRedirect(pathname) {
  const redirect = seoRedirects.find(r => r.source === pathname);
  return redirect || null;
}