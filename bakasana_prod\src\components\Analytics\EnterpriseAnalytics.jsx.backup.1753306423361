'use client';

import { useEffect, useState, Suspense } from 'react';
import Script from 'next/script';
import { usePathname, useSearchParams } from 'next/navigation';

// 🚀 ENTERPRISE ANALYTICS DOMINATION SYSTEM
// Multi-platform tracking with advanced conversion optimization

const GA4_ID = process.env.NEXT_PUBLIC_GA4_ID;
const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN;
const HOTJAR_ID = process.env.NEXT_PUBLIC_HOTJAR_ID;
const CLARITY_ID = process.env.NEXT_PUBLIC_CLARITY_ID;
const FACEBOOK_PIXEL_ID = process.env.NEXT_PUBLIC_FACEBOOK_PIXEL_ID;
const LINKEDIN_INSIGHT_ID = process.env.NEXT_PUBLIC_LINKEDIN_INSIGHT_ID;
const GOOGLE_ADS_ID = process.env.NEXT_PUBLIC_GOOGLE_ADS_ID;

function EnterpriseAnalyticsInner() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [sessionData, setSessionData] = useState({});
  const [userJourney, setUserJourney] = useState([]);

  // ========================================
  // 1. SESSION TRACKING & USER BEHAVIOR
  // ========================================
  
  useEffect(() => {
    const startTime = Date.now();
    const sessionId = `session_${startTime}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Collect advanced user data
    const userData = {
      sessionId,
      timestamp: new Date().toISOString(),
      page: pathname,
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      screenResolution: `${screen.width}x${screen.height}`,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
      deviceType: /Mobi|Android/i.test(navigator.userAgent) ? 'mobile' : 'desktop',
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      connectionType: navigator.connection?.effectiveType || 'unknown',
      connectionSpeed: navigator.connection?.downlink || 0,
      cookieEnabled: navigator.cookieEnabled,
      doNotTrack: navigator.doNotTrack === '1',
      // UTM parameters
      utmSource: searchParams.get('utm_source'),
      utmMedium: searchParams.get('utm_medium'),
      utmCampaign: searchParams.get('utm_campaign'),
      utmTerm: searchParams.get('utm_term'),
      utmContent: searchParams.get('utm_content'),
      // Facebook Click ID
      fbclid: searchParams.get('fbclid'),
      // Google Click ID
      gclid: searchParams.get('gclid')
    };
    
    setSessionData(userData);
    
    // Track page view across all platforms
    trackPageView(userData);
    
    // Initialize user journey tracking
    const journey = JSON.parse(localStorage.getItem('userJourney') || '[]');
    journey.push({
      page: pathname,
      timestamp: startTime,
      referrer: document.referrer
    });
    localStorage.setItem('userJourney', JSON.stringify(journey));
    setUserJourney(journey);
    
  }, [pathname, searchParams]);

  // ========================================
  // 2. ADVANCED EVENT TRACKING
  // ========================================
  
  useEffect(() => {
    let scrollDepth = 0;
    let timeSpent = 0;
    let interactions = 0;
    let formInteractions = 0;
    let videoEngagement = 0;
    let readingTime = 0;
    let heatmapData = [];
    
    const startTime = Date.now();
    
    // Advanced scroll tracking with milestone events
    const trackScrollDepth = () => {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const currentDepth = Math.round(((scrollTop + windowHeight) / documentHeight) * 100);
      
      if (currentDepth > scrollDepth) {
        scrollDepth = currentDepth;
        
        // Track scroll milestones
        const milestones = [25, 50, 75, 90, 100];
        milestones.forEach(milestone => {
          if (scrollDepth >= milestone && scrollDepth < milestone + 5) {
            trackEvent('scroll_milestone', {
              milestone: milestone,
              page: pathname,
              timeToReach: Date.now() - startTime,
              deviceType: sessionData.deviceType
            });
          }
        });
      }
    };
    
    // Advanced click tracking with heatmap data
    const trackClicks = (e) => {
      interactions++;
      
      const clickData = {
        x: e.clientX,
        y: e.clientY,
        element: e.target.tagName,
        className: e.target.className,
        id: e.target.id,
        href: e.target.href,
        text: e.target.textContent?.substring(0, 50),
        timestamp: Date.now()
      };
      
      heatmapData.push(clickData);
      
      // Track specific business events
      const target = e.target.closest('a, button');
      if (target) {
        const href = target.href || target.getAttribute('data-href');
        const text = target.textContent || target.getAttribute('aria-label');
        
        // Retreat booking interest
        if (href?.includes('rezerwacja') || href?.includes('booking')) {
          trackEvent('retreat_booking_interest', {
            buttonText: text,
            page: pathname,
            position: { x: e.clientX, y: e.clientY },
            userJourneyStep: userJourney.length,
            timeSpent: Date.now() - startTime
          });
          
          // Facebook Pixel conversion
          if (window.fbq) {
            window.fbq('track', 'InitiateCheckout', {
              content_name: text,
              content_category: 'Yoga Retreat',
              value: 3500,
              currency: 'PLN'
            });
          }
          
          // Google Ads conversion
          if (window.gtag) {
            window.gtag('event', 'conversion', {
              send_to: `${GOOGLE_ADS_ID}/booking-interest`,
              value: 3500,
              currency: 'PLN'
            });
          }
        }
        
        // Program interest
        if (href?.includes('program')) {
          trackEvent('program_interest', {
            programType: extractProgramType(href),
            buttonText: text,
            page: pathname,
            userJourneyStep: userJourney.length
          });
        }
        
        // Contact form interest
        if (href?.includes('kontakt')) {
          trackEvent('contact_interest', {
            contactType: extractContactType(href),
            buttonText: text,
            page: pathname,
            userJourneyStep: userJourney.length
          });
        }
        
        // WhatsApp click
        if (href?.includes('whatsapp') || href?.includes('wa.me')) {
          trackEvent('whatsapp_click', {
            buttonText: text,
            page: pathname,
            userJourneyStep: userJourney.length
          });
        }
        
        // External links
        if (href && !href.includes('bakasana-travel.blog')) {
          trackEvent('external_link_click', {
            url: href,
            buttonText: text,
            page: pathname
          });
        }
      }
    };
    
    // Form interaction tracking
    const trackFormInteractions = (e) => {
      formInteractions++;
      
      const formData = {
        formId: e.target.closest('form')?.id,
        fieldName: e.target.name,
        fieldType: e.target.type,
        fieldValue: e.target.value?.substring(0, 10), // First 10 chars only
        action: e.type,
        timestamp: Date.now()
      };
      
      trackEvent('form_interaction', {
        ...formData,
        page: pathname,
        userJourneyStep: userJourney.length
      });
      
      // Track form completion steps
      if (e.type === 'submit') {
        trackEvent('form_submit', {
          formId: formData.formId,
          page: pathname,
          timeToComplete: Date.now() - startTime,
          totalInteractions: formInteractions
        });
        
        // Facebook Pixel lead event
        if (window.fbq) {
          window.fbq('track', 'Lead', {
            content_name: 'Contact Form',
            content_category: 'Lead Generation'
          });
        }
      }
    };
    
    // Video engagement tracking
    const trackVideoEngagement = (e) => {
      videoEngagement++;
      
      const videoData = {
        videoSrc: e.target.src,
        action: e.type,
        currentTime: e.target.currentTime,
        duration: e.target.duration,
        timestamp: Date.now()
      };
      
      trackEvent('video_engagement', {
        ...videoData,
        page: pathname,
        engagementLevel: calculateEngagementLevel(videoData)
      });
    };
    
    // Reading time estimation
    const estimateReadingTime = () => {
      const text = document.body.innerText;
      const words = text.split(/\s+/).length;
      const avgWordsPerMinute = 200;
      readingTime = Math.ceil(words / avgWordsPerMinute);
    };
    
    // Time spent tracking
    const trackTimeSpent = () => {
      timeSpent = Date.now() - startTime;
      
      // Engagement milestones
      const milestones = [10, 30, 60, 120, 300]; // seconds
      milestones.forEach(milestone => {
        if (timeSpent >= milestone * 1000 && timeSpent < (milestone + 1) * 1000) {
          trackEvent('time_milestone', {
            milestone: milestone,
            page: pathname,
            scrollDepth: scrollDepth,
            interactions: interactions,
            formInteractions: formInteractions,
            videoEngagement: videoEngagement
          });
        }
      });
    };
    
    // Exit intent tracking
    const trackExitIntent = (e) => {
      if (e.clientY <= 0) {
        trackEvent('exit_intent', {
          page: pathname,
          timeSpent: Date.now() - startTime,
          scrollDepth: scrollDepth,
          interactions: interactions,
          userJourneyStep: userJourney.length
        });
      }
    };
    
    // Before unload tracking
    const trackBeforeUnload = () => {
      const finalData = {
        timeSpent: Date.now() - startTime,
        scrollDepth: scrollDepth,
        interactions: interactions,
        formInteractions: formInteractions,
        videoEngagement: videoEngagement,
        readingTime: readingTime,
        heatmapData: heatmapData,
        userJourney: userJourney
      };
      
      // Send final session data
      trackEvent('session_end', {
        ...finalData,
        page: pathname,
        sessionId: sessionData.sessionId
      });
      
      // Store session data for future analysis
      localStorage.setItem('lastSessionData', JSON.stringify(finalData));
    };
    
    // Event listeners
    window.addEventListener('scroll', trackScrollDepth, { passive: true });
    window.addEventListener('click', trackClicks);
    window.addEventListener('input', trackFormInteractions);
    window.addEventListener('change', trackFormInteractions);
    window.addEventListener('submit', trackFormInteractions);
    window.addEventListener('mouseleave', trackExitIntent);
    window.addEventListener('beforeunload', trackBeforeUnload);
    
    // Video event listeners
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      video.addEventListener('play', trackVideoEngagement);
      video.addEventListener('pause', trackVideoEngagement);
      video.addEventListener('ended', trackVideoEngagement);
    });
    
    // Time tracking interval
    const timeInterval = setInterval(trackTimeSpent, 1000);
    
    // Initial reading time calculation
    estimateReadingTime();
    
    return () => {
      window.removeEventListener('scroll', trackScrollDepth);
      window.removeEventListener('click', trackClicks);
      window.removeEventListener('input', trackFormInteractions);
      window.removeEventListener('change', trackFormInteractions);
      window.removeEventListener('submit', trackFormInteractions);
      window.removeEventListener('mouseleave', trackExitIntent);
      window.removeEventListener('beforeunload', trackBeforeUnload);
      clearInterval(timeInterval);
      
      videos.forEach(video => {
        video.removeEventListener('play', trackVideoEngagement);
        video.removeEventListener('pause', trackVideoEngagement);
        video.removeEventListener('ended', trackVideoEngagement);
      });
    };
  }, [pathname, sessionData, userJourney]);

  // ========================================
  // 3. UNIVERSAL EVENT TRACKING FUNCTION
  // ========================================
  
  const trackEvent = (eventName, eventData) => {
    // Google Analytics 4
    if (window.gtag) {
      window.gtag('event', eventName, {
        event_category: eventData.category || 'User Engagement',
        event_label: eventData.label || eventData.page,
        value: eventData.value || 1,
        custom_parameter_1: eventData.custom1,
        custom_parameter_2: eventData.custom2,
        custom_parameter_3: eventData.custom3,
        send_to: GA4_ID,
        ...eventData
      });
    }
    
    // Mixpanel
    if (window.mixpanel) {
      window.mixpanel.track(eventName, {
        ...eventData,
        $current_url: window.location.href,
        $referrer: document.referrer,
        $screen_height: screen.height,
        $screen_width: screen.width,
        mp_lib: 'web',
        $lib_version: '2.47.0'
      });
    }
    
    // Facebook Pixel
    if (window.fbq) {
      window.fbq('trackCustom', eventName, eventData);
    }
    
    // LinkedIn Insight
    if (window.lintrk) {
      window.lintrk('track', { conversion_id: eventData.conversionId || 'generic' });
    }
    
    // Microsoft Clarity
    if (window.clarity) {
      window.clarity('set', eventName, JSON.stringify(eventData));
    }
    
    // Custom analytics endpoint
    if (process.env.NEXT_PUBLIC_CUSTOM_ANALYTICS_ENDPOINT) {
      fetch(process.env.NEXT_PUBLIC_CUSTOM_ANALYTICS_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event: eventName,
          data: eventData,
          timestamp: new Date().toISOString(),
          sessionId: sessionData.sessionId
        })
      }).catch(err => console.error('Analytics error:', err));
    }
  };
  
  // ========================================
  // 4. PAGE VIEW TRACKING
  // ========================================
  
  const trackPageView = (userData) => {
    // Google Analytics 4
    if (window.gtag) {
      window.gtag('config', GA4_ID, {
        page_title: document.title,
        page_location: window.location.href,
        custom_map: {
          custom_parameter_1: 'device_type',
          custom_parameter_2: 'user_journey_step',
          custom_parameter_3: 'session_id'
        }
      });
      
      window.gtag('event', 'page_view', {
        page_title: document.title,
        page_location: window.location.href,
        device_type: userData.deviceType,
        user_journey_step: userJourney.length,
        session_id: userData.sessionId
      });
    }
    
    // Mixpanel
    if (window.mixpanel) {
      window.mixpanel.track('Page View', userData);
    }
    
    // Facebook Pixel
    if (window.fbq) {
      window.fbq('track', 'PageView', {
        content_name: document.title,
        content_category: 'Website Page'
      });
    }
  };
  
  // ========================================
  // 5. HELPER FUNCTIONS
  // ========================================
  
  const extractProgramType = (href) => {
    if (href.includes('bali')) return 'Bali Retreat';
    if (href.includes('sri-lanka')) return 'Sri Lanka Retreat';
    if (href.includes('online')) return 'Online Classes';
    return 'General Program';
  };
  
  const extractContactType = (href) => {
    if (href.includes('whatsapp')) return 'WhatsApp';
    if (href.includes('email')) return 'Email';
    if (href.includes('phone')) return 'Phone';
    return 'Contact Form';
  };
  
  const calculateEngagementLevel = (videoData) => {
    const watchPercentage = (videoData.currentTime / videoData.duration) * 100;
    if (watchPercentage > 80) return 'High';
    if (watchPercentage > 50) return 'Medium';
    if (watchPercentage > 20) return 'Low';
    return 'Minimal';
  };

  return (
    <>
      {/* Google Analytics 4 */}
      {GA4_ID && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${GA4_ID}`}
            strategy="afterInteractive"
          />
          <Script id="google-analytics" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${GA4_ID}', {
                page_title: document.title,
                page_location: window.location.href,
                debug_mode: ${process.env.NODE_ENV === 'development'},
                send_page_view: true,
                allow_google_signals: true,
                allow_ad_personalization_signals: true,
                cookie_flags: 'SameSite=None;Secure',
                anonymize_ip: false,
                custom_map: {
                  'custom_parameter_1': 'device_type',
                  'custom_parameter_2': 'user_journey_step',
                  'custom_parameter_3': 'session_id'
                }
              });
            `}
          </Script>
        </>
      )}

      {/* Mixpanel */}
      {MIXPANEL_TOKEN && (
        <Script
          id="mixpanel-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,a){if(!a.__SV){var b=window;try{var d,m,j,k=b.location,f=k.hash;d=function(a,b){return(m=a.match(RegExp(b+"=([^&]*)")))?m[1]:null};f&&d(f,"state")&&(j=JSON.parse(decodeURIComponent(d(f,"state"))),"mpeditor"===j.action&&(b.sessionStorage.setItem("_mpcehash",f),history.replaceState(j.desiredHash||"",c.title,k.pathname+k.search)))}catch(n){}var l,h;window.mixpanel=a;a._i=[];a.init=function(b,d,g){function c(b,i){var a=i.split(".");2==a.length&&(b=b[a[0]],i=a[1]);b[i]=function(){b.push([i].concat(Array.prototype.slice.call(arguments,0)))}}var e=a;"undefined"!==typeof g?e=a[g]=[]:g="mixpanel";e.people=e.people||[];e.toString=function(b){var a="mixpanel";"mixpanel"!==g&&(a+="."+g);b||(a+=" (stub)");return a};e.people.toString=function(){return e.toString(1)+".people (stub)"};l="disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove".split(" ");for(h=0;h<l.length;h++)c(e,l[h]);a._i.push([b,d,g])};a.__SV=1.2;b=c.createElement("script");b.type="text/javascript";b.async=!0;b.src="undefined"!==typeof MIXPANEL_CUSTOM_LIB_URL?MIXPANEL_CUSTOM_LIB_URL:"file:"===c.location.protocol&&"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js".match(/^\\/\\//)?"https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js":"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js";d=c.getElementsByTagName("script")[0];d.parentNode.insertBefore(b,d)}})(document,window.mixpanel||[]);
              mixpanel.init("${MIXPANEL_TOKEN}", {
                debug: ${process.env.NODE_ENV === 'development'},
                track_pageview: false,
                persistence: 'localStorage',
                api_host: 'https://api-eu.mixpanel.com',
                ignore_dnt: true,
                property_blacklist: [],
                loaded: function(mixpanel) {
                  // Mixpanel loaded
                },
                cross_site_cookie: true,
                secure_cookie: true,
                upgrade: true,
                track_marketing: true,
                track_ad_campaigns: true,
                save_referrer: true
              });
            `,
          }}
        />
      )}

      {/* Facebook Pixel */}
      {FACEBOOK_PIXEL_ID && (
        <Script
          id="facebook-pixel"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              !function(f,b,e,v,n,t,s)
              {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];t=b.createElement(e);t.async=!0;
              t.src=v;s=b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t,s)}(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
              fbq('init', '${FACEBOOK_PIXEL_ID}');
              fbq('track', 'PageView');
            `,
          }}
        />
      )}

      {/* Microsoft Clarity */}
      {CLARITY_ID && (
        <Script
          id="clarity-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "${CLARITY_ID}");
            `,
          }}
        />
      )}

      {/* Hotjar */}
      {HOTJAR_ID && (
        <Script
          id="hotjar-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:${HOTJAR_ID},hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
              })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            `,
          }}
        />
      )}

      {/* LinkedIn Insight Tag */}
      {LINKEDIN_INSIGHT_ID && (
        <Script
          id="linkedin-insight"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              _linkedin_partner_id = "${LINKEDIN_INSIGHT_ID}";
              window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
              window._linkedin_data_partner_ids.push(_linkedin_partner_id);
              (function(l) {
                if (!l){window.lintrk = function(a,b){window.lintrk.q.push([a,b])};
                window.lintrk.q=[]}
                var s = document.getElementsByTagName("script")[0];
                var b = document.createElement("script");
                b.type = "text/javascript";b.async = true;
                b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
                s.parentNode.insertBefore(b, s);
              })(window.lintrk);
            `,
          }}
        />
      )}

      {/* Google Ads Conversion Tracking */}
      {GOOGLE_ADS_ID && (
        <Script
          id="google-ads-conversion"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              gtag('config', '${GOOGLE_ADS_ID}');
              
              // Enhanced conversion tracking
              gtag('config', '${GOOGLE_ADS_ID}/booking-interest', {
                phone_number: '+48666777888',
                email_address: '<EMAIL>'
              });
            `,
          }}
        />
      )}

      {/* Custom Analytics Functions */}
      <Script
        id="custom-analytics-functions"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            // Global tracking functions for easy integration
            window.trackRetreatBooking = function(retreatType, step, value) {
              
              // GA4
              if (window.gtag) {
                gtag('event', 'retreat_booking_step', {
                  retreat_type: retreatType,
                  step: step,
                  value: value || 0,
                  currency: 'PLN',
                  event_category: 'Conversion',
                  event_label: retreatType + '_' + step
                });
              }
              
              // Mixpanel
              if (window.mixpanel) {
                mixpanel.track('Retreat Booking Step', {
                  retreat_type: retreatType,
                  step: step,
                  value: value || 0,
                  currency: 'PLN'
                });
              }
              
              // Facebook Pixel
              if (window.fbq) {
                fbq('track', 'Purchase', {
                  value: value || 0,
                  currency: 'PLN',
                  content_name: retreatType,
                  content_category: 'Yoga Retreat'
                });
              }
            };
            
            window.trackContactForm = function(formType, email) {
              
              // GA4
              if (window.gtag) {
                gtag('event', 'generate_lead', {
                  form_type: formType,
                  value: 100,
                  currency: 'PLN'
                });
              }
              
              // Mixpanel
              if (window.mixpanel && email) {
                mixpanel.identify(email);
                mixpanel.people.set({
                  '$email': email,
                  '$last_seen': new Date().toISOString(),
                  form_type: formType
                });
                mixpanel.track('Contact Form Submit', {
                  form_type: formType
                });
              }
              
              // Facebook Pixel
              if (window.fbq) {
                fbq('track', 'Lead', {
                  content_name: formType,
                  content_category: 'Contact Form'
                });
              }
            };
            
            window.trackNewsletterSignup = function(email, location) {
              
              // GA4
              if (window.gtag) {
                gtag('event', 'sign_up', {
                  method: 'email',
                  location: location
                });
              }
              
              // Mixpanel
              if (window.mixpanel && email) {
                mixpanel.identify(email);
                mixpanel.people.set({
                  '$email': email,
                  '$last_seen': new Date().toISOString(),
                  signup_location: location
                });
                mixpanel.track('Newsletter Signup', {
                  location: location
                });
              }
              
              // Facebook Pixel
              if (window.fbq) {
                fbq('track', 'Subscribe', {
                  value: 50,
                  currency: 'PLN'
                });
              }
            };
          `,
        }}
      />
    </>
  );
}

export default function EnterpriseAnalytics() {
  return (
    <Suspense fallback={null}>
      <EnterpriseAnalyticsInner />
    </Suspense>
  );
}