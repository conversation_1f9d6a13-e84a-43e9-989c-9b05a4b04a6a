'use client';

import React, { useState } from 'react';
import UnifiedButton from '@/components/ui/UnifiedButton';

export default function ContactForm() {
  const [formData, setFormData] = useState({ name: '', email: '', message: '', honeypot: '' });
  const [status, setStatus] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Validation functions
  const validateField = (name, value) => {
    switch (name) {
      case 'name':
        if (!value.trim()) return 'Imię jest wymagane';
        if (value.trim().length < 2) return 'Imię musi mieć co najmniej 2 znaki';
        if (!/^[a-zA-ZąćęłńóśźżĄĆĘŁŃÓŚŹŻ\s]+$/.test(value)) return 'Imię może zawierać tylko litery';
        return '';
      case 'email':
        if (!value.trim()) return 'Email jest wymagany';
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) return 'Podaj prawidłowy adres email';
        return '';
      case 'message':
        if (!value.trim()) return 'Wiadomość jest wymagana';
        if (value.trim().length < 10) return 'Wiadomość musi mieć co najmniej 10 znaków';
        if (value.trim().length > 1000) return 'Wiadomość nie może przekraczać 1000 znaków';
        return '';
      default:
        return '';
    }
  };

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData({ ...formData, [id]: value });
    
    // Real-time validation
    if (touched[id]) {
      const error = validateField(id, value);
      setErrors(prev => ({ ...prev, [id]: error }));
    }
  };

  const handleBlur = (e) => {
    const { id, value } = e.target;
    setTouched(prev => ({ ...prev, [id]: true }));
    const error = validateField(id, value);
    setErrors(prev => ({ ...prev, [id]: error }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Honeypot check - jeśli wypełnione, to spam
    if (formData.honeypot) {
      return;
    }

    // Validate all fields
    const newErrors = {};
    Object.keys(formData).forEach(key => {
      if (key !== 'honeypot') {
        const error = validateField(key, formData[key]);
        if (error) newErrors[key] = error;
      }
    });

    setErrors(newErrors);
    setTouched({ name: true, email: true, message: true });

    // If there are errors, don't submit
    if (Object.keys(newErrors).length > 0) {
      setStatus('Proszę poprawić błędy w formularzu');
      setTimeout(() => setStatus(''), 5000);
      return;
    }

    setIsSubmitting(true);
    setStatus('Wysyłanie...');

    try {
      const response = await fetch('https://api.web3forms.com/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          access_key: 'YOUR_WEB3FORMS_ACCESS_KEY', // Zastąp swoim kluczem z web3forms.com
          name: formData.name,
          email: formData.email,
          message: formData.message,
          subject: `Nowa wiadomość z BAKASANA od ${formData.name}`,
          from_name: 'BAKASANA',
          to_email: '<EMAIL>'
        })
      });

      const result = await response.json();

      if (result.success) {
        setStatus('Wiadomość wysłana. Dziękujemy, odpowiemy wkrótce.');
        setFormData({ name: '', email: '', message: '', honeypot: '' });
        setErrors({});
        setTouched({});
      } else {
        throw new Error('Błąd wysyłania');
      }
    } catch (error) {
      console.error('Error:', error);
      setStatus('Wystąpił błąd. Spróbuj ponownie lub napisz bezpoś<NAME_EMAIL>');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setStatus(''), 8000);
    }
  };

  const socialLinks = [
    {
      href: "https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr",
      label: "Instagram",
      aria: "Profil na Instagramie"
    },
    {
      href: "https://www.facebook.com/p/Fly-with-bakasana-100077568306563/",
      label: "Facebook",
      aria: "Profil na Facebooku"
    },
    {
      href: "mailto:<EMAIL>",
      label: "Email",
      aria: "Kontakt email"
    }
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-start max-w-5xl mx-auto">
      {/* FORMULARZ KONTAKTOWY - Ultra-minimal */}
      <div className="space-y-lg">
        <div className="text-center lg:text-left">
          <h2 className="section-header mb-md">Napisz do nas</h2>
          <p className="body-text opacity-80">
            Każda wiadomość jest dla nas ważna
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-lg">
          <div>
            <label htmlFor="name" className="block subtle-text mb-3">Imię</label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={handleChange}
              onBlur={handleBlur}
              required
              className={`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 focus:outline-none ${
                errors.name ? 'border-red-500 focus:border-red-500' : 'border-stone/30 focus:border-charcoal-gold'
              }`}
              placeholder="Twoje imię"
              aria-invalid={errors.name ? 'true' : 'false'}
              aria-describedby={errors.name ? 'name-error' : undefined}
            />
            {errors.name && (
              <p id="name-error" className="text-red-500 text-sm mt-2" role="alert">
                {errors.name}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="email" className="block subtle-text mb-3">Email</label>
            <input
              type="email"
              id="email"
              value={formData.email}
              onChange={handleChange}
              onBlur={handleBlur}
              required
              className={`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 focus:outline-none ${
                errors.email ? 'border-red-500 focus:border-red-500' : 'border-stone/30 focus:border-charcoal-gold'
              }`}
              placeholder="<EMAIL>"
              aria-invalid={errors.email ? 'true' : 'false'}
              aria-describedby={errors.email ? 'email-error' : undefined}
            />
            {errors.email && (
              <p id="email-error" className="text-red-500 text-sm mt-2" role="alert">
                {errors.email}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="message" className="block subtle-text mb-3">
              Wiadomość
              <span className="text-xs text-stone/60 ml-2">
                ({formData.message.length}/1000)
              </span>
            </label>
            <textarea
              id="message"
              value={formData.message}
              onChange={handleChange}
              onBlur={handleBlur}
              required
              rows={6}
              maxLength={1000}
              className={`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 resize-none focus:outline-none ${
                errors.message ? 'border-red-500 focus:border-red-500' : 'border-stone/30 focus:border-charcoal-gold'
              }`}
              placeholder="Podziel się swoimi myślami..."
              aria-invalid={errors.message ? 'true' : 'false'}
              aria-describedby={errors.message ? 'message-error' : undefined}
            />
            {errors.message && (
              <p id="message-error" className="text-red-500 text-sm mt-2" role="alert">
                {errors.message}
              </p>
            )}
          </div>

          {/* Honeypot field - ukryte dla ludzi, widoczne dla botów */}
          <input
            type="text"
            id="honeypot"
            name="honeypot"
            value={formData.honeypot}
            onChange={handleChange}
            className="hidden"
            tabIndex="-1"
            autoComplete="off"
          />

          <div className="pt-8">
            <button
              type="submit"
              disabled={isSubmitting || Object.keys(errors).some(key => errors[key])}
              className={`btn-ghost btn-primary relative ${
                isSubmitting || Object.keys(errors).some(key => errors[key]) 
                  ? 'opacity-50 cursor-not-allowed' 
                  : 'hover:opacity-80'
              }`}
            >
              {isSubmitting && (
                <span className="absolute left-4 top-1/2 transform -translate-y-1/2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                </span>
              )}
              <span className={isSubmitting ? 'ml-6' : ''}>
                {isSubmitting ? 'Wysyłanie...' : 'Wyślij Wiadomość'}
              </span>
            </button>

            {status && (
              <p className={`text-sm font-light mt-sm max-w-xs ${
                status.includes('błąd') || status.includes('Proszę poprawić') 
                  ? 'text-red-500' 
                  : status.includes('wysłana') 
                    ? 'text-green-600' 
                    : 'text-charcoal/70'
              }`}>
                {status}
              </p>
            )}
          </div>
        </form>
      </div>

      {/* KONTAKT - Ultra-minimal */}
      <div className="space-y-lg">
        <div className="text-center lg:text-left">
          <h3 className="section-header mb-md">Znajdź nas</h3>
          <p className="body-text opacity-80 mb-lg">
            Połączmy się w przestrzeni cyfrowej
          </p>
        </div>

        <div className="space-y-md">
          {socialLinks.map((link) => (
            <a
              key={link.label}
              href={link.href}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={link.aria}
              className="block p-6 hover:opacity-70 transition-opacity duration-200 text-center lg:text-left"
            >
              <h4 className="font-light text-charcoal mb-2 tracking-wide text-lg">
                {link.label}
              </h4>
              <p className="text-sm text-stone font-light">
                {link.label === 'Instagram' && 'Codzienne inspiracje'}
                {link.label === 'Facebook' && 'Społeczność BAKASANA'}
                {link.label === 'Email' && 'Bezpośredni kontakt'}
              </p>
            </a>
          ))}
        </div>

        {/* SACRED DIVIDER */}
        <div className="flex items-center justify-center lg:justify-start my-12">
          <div className="flex items-center gap-sm text-charcoal-gold/60">
            <div className="w-12 h-px bg-charcoal-gold/30"></div>
            <span className="text-lg opacity-60">ॐ</span>
            <div className="w-12 h-px bg-charcoal-gold/30"></div>
          </div>
        </div>

        <div className="text-center lg:text-left">
          <p className="text-sm text-stone font-light italic tracking-wide">
            "Każda podróż zaczyna się od jednego kroku..."
          </p>
          <p className="text-xs text-charcoal-gold font-light tracking-wide uppercase mt-2">
            Om Swastiastu
          </p>
        </div>
      </div>
    </div>
  );
}