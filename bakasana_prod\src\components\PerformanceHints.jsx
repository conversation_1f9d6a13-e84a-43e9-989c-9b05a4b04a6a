
// BAKASANA Performance Hints
// Auto-generated resource hints for optimal loading

export const performanceHints = {
  // Critical resources to preload
  preload: [
    { href: '/images/background/bali-hero.webp', as: 'image', type: 'image/webp' },
    { href: '/fonts/cormorant-garamond-v16-latin-300.woff2', as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' },
    { href: '/fonts/inter-v13-latin-400.woff2', as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' }
  ],
  
  // Resources to prefetch
  prefetch: [
    { href: '/images/gallery/bali-1.webp', as: 'image' },
    { href: '/images/gallery/bali-2.webp', as: 'image' }
  ],
  
  // DNS prefetch for external resources
  dnsPrefetch: [
    '//fonts.googleapis.com',
    '//fonts.gstatic.com',
    '//images.unsplash.com',
    '//cdn.sanity.io'
  ]
};

// Component to inject performance hints
export default function PerformanceHints() {
  return (
    <>
      {/* Preload critical resources */}
      {performanceHints.preload.map((hint, index) => (
        <link
          key={`preload-${index}`}
          rel="preload"
          href={hint.href}
          as={hint.as}
          type={hint.type}
          crossOrigin={hint.crossOrigin}
        />
      ))}
      
      {/* Prefetch non-critical resources */}
      {performanceHints.prefetch.map((hint, index) => (
        <link
          key={`prefetch-${index}`}
          rel="prefetch"
          href={hint.href}
          as={hint.as}
        />
      ))}
      
      {/* DNS prefetch for external domains */}
      {performanceHints.dnsPrefetch.map((domain, index) => (
        <link
          key={`dns-prefetch-${index}`}
          rel="dns-prefetch"
          href={domain}
        />
      ))}
    </>
  );
}
