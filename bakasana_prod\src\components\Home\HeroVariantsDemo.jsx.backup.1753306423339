'use client';

import React, { useState } from 'react';
import BakasanaHero from './BakasanaHero';
import UnifiedButton from '@/components/ui/UnifiedButton';
import ElegantBakasanaHero from './ElegantBakasanaHero';
import MinimalistYogaHero from './MinimalistYogaHero';
import CustomColorHero from './CustomColorHero';

const HeroVariantsDemo = () => {
  const [activeVariant, setActiveVariant] = useState('original');

  const variants = [
    {
      id: 'original',
      name: 'Oryginalny BakasanaHero',
      description: 'Istniejący komponent z aktualizacją roku na 2021',
      component: <BakasanaHero />
    },
    {
      id: 'elegant',
      name: 'Elegant Bakasana Hero',
      description: 'Wersja z ulepszonymi animacjami i efektami',
      component: <ElegantBakasanaHero />
    },
    {
      id: 'minimalist',
      name: 'Minimalist Yoga Hero',
      description: 'Minimalistyczna wersja z paletą kremowy/beżowy/brązowy',
      component: <MinimalistYogaHero />
    },
    {
      id: 'custom',
      name: 'Custom Color Hero',
      description: '<PERSON>rs<PERSON> z dokładnymi kolorami (#F5E6D3 tło, #8B4513 tekst)',
      component: <CustomColorHero />
    }
  ];

  const currentVariant = variants.find(v => v.id === activeVariant);

  return (
    <div className="min-h-screen">
      {/* Navigation Panel */}
      <div className="fixed top-4 left-4 z-50 bg-white/95 backdrop-blur-sm p-4 rectangular shadow-lg max-w-xs">
        <h3 className="font-semibold mb-3 text-charcoal">Hero Section Variants</h3>
        <div className="space-y-2">
          {variants.map((variant) => (
            <button
              key={variant.id}
              onClick={() => setActiveVariant(variant.id)}
              className={`w-full text-left p-2 text-sm transition-colors ${
                activeVariant === variant.id
                  ? 'bg-charcoal-gold text-white'
                  : 'bg-gray-100 text-charcoal hover:bg-gray-200'
              }`}
            >
              <div className="font-medium">{variant.name}</div>
              <div className="text-xs opacity-75 mt-1">{variant.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Current Variant Display */}
      <div className="relative">
        {currentVariant?.component}
      </div>

      {/* Info Panel */}
      <div className="fixed bottom-4 right-4 z-50 bg-white/95 backdrop-blur-sm p-4 rectangular shadow-lg max-w-sm">
        <h4 className="font-semibold text-charcoal mb-2">Aktualnie wyświetlane:</h4>
        <div className="text-sm text-charcoal/80">
          <div className="font-medium">{currentVariant?.name}</div>
          <div className="mt-1">{currentVariant?.description}</div>
        </div>
      </div>
    </div>
  );
};

export default HeroVariantsDemo;