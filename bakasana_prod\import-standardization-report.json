{"timestamp": "2025-07-23T21:33:43.440Z", "stats": {"totalFiles": 222, "processedFiles": 222, "errors": 0, "successRate": "100.00"}, "fixes": [{"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\utils.js", "type": "missing-semicolon", "description": "Added missing semicolons to imports"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useInView.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useFormValidation.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useAdvancedAnimations.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\eventData.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\contactData.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\blogPosts.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\index.js", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\index.js", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\sitemap.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\metadata.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\newsletter\\route.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\fitssey\\webhook\\route.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\booking\\route.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\verify\\route.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\login\\route.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\login\\route.js", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\route.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\[id]\\route.js", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WebVitals.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\TrustBadges.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\TransformationCTA.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Toast.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\TestimonialSlider.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SmartBreadcrumbs.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SmartBreadcrumbs.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEOBreadcrumbs.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEOBreadcrumbs.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ScrollReveal.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ScrollReveal.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SanityTestimonials.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SanityRetreats.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\RetreatCalendar.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ResponsiveCheckerWrapper.jsx", "type": "missing-semicolon", "description": "Added missing semicolons to imports"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ResponsiveCheckerWrapper.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWAInstaller.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWAInstaller.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PerfectNavbar.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PerfectNavbar.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OptimizedImage.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OptimizedImage.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OptimizedBreadcrumbs.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OptimizedBreadcrumbs.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\OnlineClassesStyles.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\NewsletterSignup.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\NewsletterSignup.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\MinimalistNavbar.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\MinimalistNavbar.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\MinimalistHero.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\MinimalistHero.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\InternalLinks.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\InternalLinks.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\InteractiveMap.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\InteractiveMap.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\GhostNavbar.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\GhostNavbar.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FormPreconnect.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FitsseySchedule.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FitsseySchedule.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FitsseyIntegration.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FitsseyIntegration.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FAQSection.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\FAQSection.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\CoreWebVitals.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\CookieConsent.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ConditionalNavbar.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientOnlyResponsiveChecker.jsx", "type": "missing-semicolon", "description": "Added missing semicolons to imports"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientLayout.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientLayout.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ClientInteractiveButton.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Breadcrumbs.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Breadcrumbs.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\BookingForm.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\BookingForm.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\BookingCalendar.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\BookingCalendar.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\AnimatedCounter.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\WorldClassProvider.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\PerformanceMonitor.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\MagneticButton.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\KeyboardShortcuts.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\AccessibilityEnhancer.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedTypography.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedInput.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedCard.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\UnifiedButton.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\textarea.jsx", "type": "missing-semicolon", "description": "Added missing semicolons to imports"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\SmoothScrollProvider.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\SkeletonLoader.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\Section.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\RippleButton.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\RippleButton.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ResponsiveGrid.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\QualityAssuranceWrapper.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\QualityAssurance.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ParallaxSection.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ParallaxSection.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\PageTransition.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\PageTransition.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\PageLoader.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\OptimizedLazyImage.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\OptimizedLazyImage.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\OptimizedImage.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\LazyImage.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\LazyImage.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\label.jsx", "type": "missing-semicolon", "description": "Added missing semicolons to imports"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\input.jsx", "type": "missing-semicolon", "description": "Added missing semicolons to imports"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\IconSystem.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\GlassCard.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\GlassCard.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ErrorBoundary.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ErrorBoundary.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\EnhancedButton.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\EnhancedButton.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\ElegantQuote.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\card.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\button.jsx", "type": "missing-semicolon", "description": "Added missing semicolons to imports"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\button.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\BlogComponents.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\badge.jsx", "type": "missing-semicolon", "description": "Added missing semicolons to imports"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\WeatherWidget.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\TravelGuide.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\InteractiveMap.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\InteractiveMap.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\DestinationCard.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\DestinationCard.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Travel\\CurrencyConverter.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\SchemaOrg.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\MetaTags.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\EnterpriseMetaTags.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\ContactStructuredData.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\CompetitorAnalysis.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\CanonicalURL.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\SEO\\AdvancedSEO.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\PWAInstall.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\RealUserMonitoring.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\PerformanceMonitor.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\ImageOptimizer.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\ImageOptimizer.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navbar\\ClientNavbar.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navbar\\ClientNavbar.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\WellnessPage.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\WellnessPage.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\SimpleHero.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\ProfessionalHomePage.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\ProfessionalHero.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\OnlineClassesSection.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\MinimalistYogaHero.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\HeroVariantsDemo.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\ElegantBakasanaHero.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\CustomColorHero.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Home\\BakasanaHero.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\HighlightCard\\index.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\ServerFooter.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\index.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Events\\EventCard.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Events\\EventCard.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ErrorBoundary\\AdvancedErrorBoundary.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Booking\\EnhancedBookingFlow.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\TravelAnalytics.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\SuperiorAnalytics.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\SuperiorAnalytics.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\SEOTracker.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\LazyAnalytics.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\index.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\EnterpriseAnalytics.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\EnterpriseAnalytics.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\ConversionOptimization.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\AdvancedAnalytics.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\AdvancedAnalytics.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Analytics\\ABTestingEngine.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\accessibility\\AccessibilityProvider.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\accessibility\\AccessibilityProvider.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\accessibility\\AccessibilityEnhancer.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\opengraph-image.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found-bali.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\layout.jsx", "type": "missing-semicolon", "description": "Added missing semicolons to imports"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\layout.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\error.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-stacjonarne\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-stacjonarne\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\yoga-retreat-z-polski\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\yoga-retreat-z-polski\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\wellness\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\transformacyjne-podroze-azja\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\transformacyjne-podroze-azja\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\rezerwacja\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\rezerwacja\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty-jogi-bali-2025\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty-jogi-bali-2025\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\srilanka\\page.jsx", "type": "missing-semicolon", "description": "Added missing semicolons to imports"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\srilanka\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\polityka-prywatnosci\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\polityka-prywatnosci\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\mapa\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\juli<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\juli<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\joga-sri-lanka-retreat\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\joga-sri-lanka-retreat\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\BlogPageClientContent.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\admin\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\admin\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\admin\\bookings\\page.jsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\admin\\bookings\\page.jsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Services\\OldMoneyServices.tsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Services\\OldMoneyServices.tsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\OldMoneyNavbar.tsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Navigation\\OldMoneyNavbar.tsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Hero\\OldMoneyHero.tsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Hero\\OldMoneyHero.tsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\OldMoneyFooter.tsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\OldMoneyFooter.tsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\About\\OldMoneyAbout.tsx", "type": "import-spacing", "description": "Fixed spacing in import statements"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\About\\OldMoneyAbout.tsx", "type": "import-order", "description": "Reordered imports according to standard"}, {"file": "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\old-money\\page.tsx", "type": "import-order", "description": "Reordered imports according to standard"}], "summary": {"totalFiles": 222, "fixedFiles": 169, "successRate": "100.00"}}