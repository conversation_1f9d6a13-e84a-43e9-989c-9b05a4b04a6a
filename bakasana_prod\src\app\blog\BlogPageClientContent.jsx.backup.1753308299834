import Image from 'next/image';
import Link from 'next/link';
import React, { useMemo } from 'react';

export default function BlogPageClientContent({ posts = [] }) {

'use client';


// Magazine Style Blog Card
const PostCard = ({ post, featured = false, className = '' }) => {
  if (!post) return null;

  const cardClass = featured ? 'magazine-card-featured' : 'magazine-card';

  return (
    <article className={`${cardClass} ${className}`}>
      <Link href={`/blog/${post.slug || '#'}`} className="magazine-card-link">
        {/* IMAGE SECTION */}
        <div className="magazine-card-image">
          <div 
            className="magazine-image-bg"
            style={{
              backgroundImage: `url(${post.imageUrl || '/images/placeholder/image.jpg'})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            <div className="magazine-image-overlay">
              <span className="magazine-category">
                {post.category || 'Zapisk<PERSON> z podróży'}
              </span>
            </div>
          </div>
        </div>

        {/* CONTENT SECTION */}
        <div className="magazine-card-content">
          <h3 className="magazine-card-title">
            {post.title || 'Bez tytułu'}
          </h3>

          <p className="magazine-card-excerpt">
            {post.excerpt || ''}
          </p>

          <div className="magazine-card-footer">
            <span className="magazine-read-more">Czytaj więcej</span>
            {post.readTime && (
              <span className="magazine-read-time">{post.readTime} min</span>
            )}
          </div>
        </div>
      </Link>
    </article>
  );
};

  const memoizedPosts = useMemo(() => {
    if (!Array.isArray(posts)) return [];
    return posts.filter(post => post && typeof post === 'object');
  }, [posts]);

  return (
    <div className="bg-sanctuary min-h-screen">
      {/* HERO SECTION - Magazine Style Header */}
      <section className="magazine-hero">
        <div className="magazine-hero-content">
          <div className="magazine-header-line"></div>
          
          <h1 className="magazine-title">
            Zapiski
          </h1>

          <p className="magazine-subtitle">
            Historie napisane sercem
          </p>

          <div className="magazine-meta">
            Bali • Sri Lanka • Wewnętrzne podróże
          </div>
          
          <div className="magazine-header-line"></div>
        </div>
      </section>

      {/* BLOG POSTS - Magazine Editorial Layout */}
      <section className="magazine-content">
        {memoizedPosts.length > 0 ? (
          <div className="magazine-grid">
            {/* Featured Article - Large */}
            {memoizedPosts[0] && (
              <article className="magazine-featured">
                <PostCard post={memoizedPosts[0]} featured={true} className="featured-card" />
              </article>
            )}

            {/* Secondary Articles - Medium */}
            <div className="magazine-secondary">
              {memoizedPosts.slice(1, 3).map((post, index) => (
                <article key={`secondary-${post?.slug || index}`} className="magazine-secondary-item">
                  <PostCard post={post} className="secondary-card" />
                </article>
              ))}
            </div>

            {/* Additional Articles - Small Grid */}
            {memoizedPosts.length > 3 && (
              <div className="magazine-grid-small">
                {memoizedPosts.slice(3).map((post, index) => (
                  <article key={`small-${post?.slug || index}`} className="magazine-small-item">
                    <PostCard post={post} className="small-card" />
                  </article>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="magazine-empty">
            <div className="magazine-empty-content">
              <h3 className="magazine-empty-title">Wkrótce więcej treści</h3>
              <p className="magazine-empty-text">Pracujemy nad nowymi inspirującymi artykułami</p>
            </div>
          </div>
        )}
      </section>

      {/* COMMUNITY SECTION - BAKASANA Standards */}
      <section className="container">
        <div className="text-center space-y-lg max-w-3xl mx-auto">
          <div className="space-y-md">
            <h3 className="section-header">
              Bądź na bieżąco
            </h3>

            <p className="body-text opacity-80">
              Otrzymuj najnowsze artykuły i inspiracje z duchowych podróży
            </p>
          </div>

          {/* SACRED DIVIDER */}
          <div className="flex items-center justify-center my-12">
            <div className="flex items-center gap-sm text-charcoal-gold/60">
              <div className="w-12 h-px bg-charcoal-gold/30"></div>
              <span className="text-lg opacity-60">ॐ</span>
              <div className="w-12 h-px bg-charcoal-gold/30"></div>
            </div>
          </div>

          {/* CONTACT LINKS - Ghost buttons */}
          <div className="flex flex-col sm:flex-row gap-lg justify-center items-center">
            <a
              href="https://www.instagram.com/fly_with_bakasana"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-ghost"
            >
              Instagram
            </a>
            <a
              href="mailto:<EMAIL>"
              className="btn-ghost"
            >
              Email
            </a>
          </div>

          <div className="pt-8">
            <p className="text-sm text-stone font-light italic tracking-wide">
              "Każda historia ma swoją moc..."
            </p>
            <p className="text-xs text-charcoal-gold font-light tracking-wide uppercase mt-2">
              Om Swastiastu
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}