const fs = require('fs');
const path = require('path');

const filesToFix = [
  'src/app/program/srilanka/page.jsx',
  'src/app/retreaty-jogi-bali-2025/page.jsx', 
  'src/app/rezerwacja/page.jsx',
  'src/app/transformacyjne-podroze-azja/page.jsx'
];

filesToFix.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath);
  
  if (fs.existsSync(fullPath)) {
    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Fix the export default placement
    content = content.replace(
      /export const metadata = ([^}]+}[^}]*});?\s*export default function ([^{]+)\{/s,
      (match, metadataContent, functionName) => {
        return `export const metadata = ${metadataContent};\n\nexport default function ${functionName.trim()}{`;
      }
    );
    
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`Fixed: ${filePath}`);
  } else {
    console.log(`File not found: ${filePath}`);
  }
});

console.log('Export fixes completed!');