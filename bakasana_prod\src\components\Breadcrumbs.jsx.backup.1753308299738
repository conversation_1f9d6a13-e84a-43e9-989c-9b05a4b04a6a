import { usePathname  } from 'next/navigation';
import Link from 'next/link';

import OptimizedIcon from './OptimizedIcon';

export default Breadcrumbs;

'use client';


const Breadcrumbs = ({ customItems = null }) => {
  const pathname = usePathname();
  
  // <PERSON><PERSON><PERSON> mamy custom items, użyj ich
  if (customItems) {
    return (
      <nav aria-label="Breadcrumb" className="mb-md">
        <ol className="flex items-center space-x-2 text-sm">
          {customItems.map((item, index) => (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <OptimizedIcon 
                  name="ChevronRight" 
                  className="w-4 h-4 text-enterprise-brown/40 mx-2" 
                />
              )}
              {item.href ? (
                <Link 
                  href={item.href}
                  className="text-enterprise-brown/70 hover:text-enterprise-brown transition-colors duration-300"
                >
                  {item.label}
                </Link>
              ) : (
                <span className="text-enterprise-brown font-medium">{item.label}</span>
              )}
            </li>
          ))}
        </ol>
        
        {/* Structured Data dla Breadcrumbs */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'BreadcrumbList',
              itemListElement: customItems.map((item, index) => ({
                '@type': 'ListItem',
                position: index + 1,
                name: item.label,
                item: item.href ? `https://bakasana-travel.blog${item.href}` : undefined
              }))
            })
          }}
        />
      </nav>
    );
  }

  // Auto-generuj breadcrumbs z pathname
  const pathSegments = pathname.split('/').filter(Boolean);
  
  const breadcrumbItems = [
    { label: 'Strona główna', href: '/' },
    ...pathSegments.map((segment, index) => {
      const href = '/' + pathSegments.slice(0, index + 1).join('/');
      const label = getSegmentLabel(segment);
      
      return {
        label,
        href: index === pathSegments.length - 1 ? null : href // Ostatni element bez linku
      };
    })
  ];

  return (
    <nav aria-label="Breadcrumb" className="mb-md">
      <ol className="flex items-center space-x-2 text-sm">
        {breadcrumbItems.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <OptimizedIcon 
                name="ChevronRight" 
                className="w-4 h-4 text-enterprise-brown/40 mx-2" 
              />
            )}
            {item.href ? (
              <Link 
                href={item.href}
                className="text-enterprise-brown/70 hover:text-enterprise-brown transition-colors duration-300"
              >
                {item.label}
              </Link>
            ) : (
              <span className="text-enterprise-brown font-medium">{item.label}</span>
            )}
          </li>
        ))}
      </ol>
      
      {/* Structured Data dla Breadcrumbs */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'BreadcrumbList',
            itemListElement: breadcrumbItems.map((item, index) => ({
              '@type': 'ListItem',
              position: index + 1,
              name: item.label,
              item: item.href ? `https://bakasana-travel.blog${item.href}` : undefined
            }))
          })
        }}
      />
    </nav>
  );
};

// Funkcja pomocnicza do mapowania segmentów URL na czytelne nazwy
function getSegmentLabel(segment) {
  const labelMap = {
    'blog': 'Blog',
    'program': 'Program',
    'galeria': 'Galeria',
    'kontakt': 'Kontakt',
    'o-mnie': 'O mnie',
    'zajecia-online': 'Zajęcia online',
    'mapa': 'Mapa Bali',
    'rezerwacja': 'Rezerwacja',
    'polityka-prywatnosci': 'Polityka prywatności',
    'stanie-na-rekach-odkryj-swoja-wewnetrzna-sile-i-odwage': 'Stanie na Rękach',
    'szpagaty-otworz-biodra-i-uwolnij-swoja-kobiecosc': 'Szpagaty',
    'kobieca-sila-w-jodze-odkryj-swoja-wewnetrzna-boginie': 'Kobieca Siła w Jodze'
  };
  
  return labelMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
}

