import { NextResponse } from 'next/server';
import crypto from 'crypto';

// Webhook endpoint dla integracji z <PERSON>tssey
// Obsługuje powiadomienia o zapisach, anulowaniach, płatnościach itp.

const WEBHOOK_SECRET = process.env.FITSSEY_WEBHOOK_SECRET;

// Validate webhook secret is configured
if (!WEBHOOK_SECRET) {
  console.warn('FITSSEY_WEBHOOK_SECRET not configured - webhook security disabled');
}

export async function POST(request) {
  try {
    // Weryfikacja podpisu webhook
    const signature = request.headers.get('x-fitssey-signature');
    const body = await request.text();

    // Verify webhook signature if secret is configured
    if (WEBHOOK_SECRET && !verifyWebhookSignature(body, signature, WEBHOOK_SECRET)) {
      console.error('Invalid webhook signature received');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    const data = JSON.parse(body);
    const { event_type, data: eventData } = data;

    console.log('Fitssey webhook received:', { event_type, eventData });

    switch (event_type) {
      case 'booking.created':
        await handleBookingCreated(eventData);
        break;
        
      case 'booking.cancelled':
        await handleBookingCancelled(eventData);
        break;
        
      case 'payment.completed':
        await handlePaymentCompleted(eventData);
        break;
        
      case 'class.updated':
        await handleClassUpdated(eventData);
        break;
        
      default:
        console.log('Unhandled webhook event:', event_type);
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Webhook processed successfully' 
    });

  } catch (error) {
    console.error('Fitssey webhook error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Webhook processing failed',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Obsługa nowej rezerwacji
async function handleBookingCreated(data) {
  const {
    booking_id,
    customer_email,
    customer_name,
    class_name,
    class_date,
    class_time,
    amount_paid
  } = data;

  console.log('New booking created:', {
    booking_id,
    customer_email,
    class_name,
    class_date
  });

  // TODO: Implementacja
  // - Zapisanie do bazy danych
  // - Wysłanie emaila potwierdzającego
  // - Dodanie do newslettera (jeśli klient wyraził zgodę)
  // - Aktualizacja statystyk

  try {
    // Przykład: dodanie do newslettera
    if (customer_email) {
      await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/newsletter`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: customer_email,
          tags: ['fitssey-customer', 'class-booking'],
          source: 'fitssey-webhook'
        })
      });
    }

    // Przykład: wysłanie emaila powitalnego
    await sendWelcomeEmail({
      email: customer_email,
      name: customer_name,
      className: class_name,
      classDate: class_date,
      classTime: class_time
    });

  } catch (error) {
    console.error('Error processing booking created:', error);
  }
}

// Obsługa anulowania rezerwacji
async function handleBookingCancelled(data) {
  const {
    booking_id,
    customer_email,
    class_name,
    cancellation_reason
  } = data;

  console.log('Booking cancelled:', {
    booking_id,
    customer_email,
    class_name,
    cancellation_reason
  });

  // TODO: Implementacja
  // - Aktualizacja statusu w bazie danych
  // - Wysłanie emaila z potwierdzeniem anulowania
  // - Aktualizacja dostępności miejsc
}

// Obsługa zakończonej płatności
async function handlePaymentCompleted(data) {
  const {
    payment_id,
    booking_id,
    amount,
    currency,
    payment_method
  } = data;

  console.log('Payment completed:', {
    payment_id,
    booking_id,
    amount,
    currency
  });

  // TODO: Implementacja
  // - Aktualizacja statusu płatności
  // - Wysłanie faktury/paragonu
  // - Aktualizacja statystyk finansowych
}

// Obsługa aktualizacji zajęć
async function handleClassUpdated(data) {
  const {
    class_id,
    class_name,
    changes,
    affected_bookings
  } = data;

  console.log('Class updated:', {
    class_id,
    class_name,
    changes,
    affected_bookings_count: affected_bookings?.length || 0
  });

  // TODO: Implementacja
  // - Powiadomienie uczestników o zmianach
  // - Aktualizacja harmonogramu na stronie
  // - Obsługa anulowań/przeniesień
}

// Wysłanie emaila powitalnego
async function sendWelcomeEmail({ email, name, className, classDate, classTime }) {
  // TODO: Integracja z serwisem email (Resend, SendGrid, etc.)
  
  const emailContent = `
    Cześć ${name}!
    
    Dziękujemy za zapisanie się na zajęcia w studio BAKASANA!
    
    Szczegóły Twojej rezerwacji:
    • Zajęcia: ${className}
    • Data: ${classDate}
    • Godzina: ${classTime}
    • Lokalizacja: ul. Przykładowa 123, Warszawa
    
    Pamiętaj o:
    • Przyjściu 10 minut przed zajęciami
    • Zabraniu własnej maty (lub wypożyczeniu na miejscu)
    • Wygodnym ubraniu do ćwiczeń
    
    Jeśli masz pytania, skontaktuj się z nami:
    📞 +48 606 101 523
    📧 <EMAIL>
    
    Do zobaczenia na macie!
    
    Namaste,
    Julia Jakubowicz
    BAKASANA Studio
  `;

  console.log('Welcome email would be sent to:', email);
  // W produkcji: rzeczywiste wysłanie emaila
}

// Weryfikacja podpisu webhook używając HMAC SHA256
function verifyWebhookSignature(payload, signature, secret) {
  if (!signature || !secret) {
    return false;
  }

  try {
    // Remove 'sha256=' prefix if present
    const cleanSignature = signature.replace(/^sha256=/, '');

    // Generate expected signature
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload, 'utf8')
      .digest('hex');

    // Use timing-safe comparison
    return crypto.timingSafeEqual(
      Buffer.from(cleanSignature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    console.error('Webhook signature verification error:', error);
    return false;
  }
}

// GET endpoint dla testowania
export async function GET() {
  return NextResponse.json({
    message: 'Fitssey webhook endpoint is working',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
}