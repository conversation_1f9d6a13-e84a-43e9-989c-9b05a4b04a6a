# 🎨 BAKASANA - UNIFIED DESIGN SYSTEM GUIDE

## 📋 Spis treści
1. [Kolory](#kolory)
2. [Typografia](#typografia)
3. [Ikony](#ikony)
4. [Komponenty](#komponenty)
5. [Zasady kodowania](#zasady-kodowania)
6. [<PERSON><PERSON><PERSON><PERSON>](#migracja)

---

## 🎨 Kolory

### Główna paleta kolorów
```css
/* Podstawowe kolory */
--warm-sanctuary: #FDFCF8;     /* Tło główne */
--warm-charcoal: #2A2724;      /* Tekst główny */
--warm-enterprise: #8B7355;    /* Ak<PERSON> główny */
--warm-terra: #B8935C;         /* Akcent ciepły */

/* Neutralne */
--sage: #9CA3AF;               /* Tekst pomocniczy */
--stone: #6B7280;              /* Tekst drugorzędny */
--whisper: #F9FAFB;            /* Tło sekcji */
```

### ✅ DOBRE PRAKTYKI
```jsx
// ✅ Używaj nowych nazw kolorów
className="text-charcoal bg-sanctuary border-enterprise-brown"

// ✅ Używaj unified components
<UnifiedButton variant="primary">Zarezerwuj</UnifiedButton>
```

### ❌ UNIKAJ
```jsx
// ❌ Stare nazwy kolorów
className="text-temple bg-rice border-temple-gold"

// ❌ Hardcoded kolory
className="text-gray-800 bg-yellow-50"
```

---

## 📝 Typografia

### Komponenty typograficzne
```jsx
import { 
  HeroTitle, 
  SectionTitle, 
  CardTitle, 
  BodyText, 
  LeadText 
} from '@/components/ui/UnifiedTypography';

// Hero sections
<HeroTitle>Retreaty Jogi na Bali</HeroTitle>

// Section headings
<SectionTitle>Nasze programy</SectionTitle>

// Card titles
<CardTitle>Retreat na Sri Lanka</CardTitle>

// Body text
<BodyText>Opis programu retreatu...</BodyText>

// Lead paragraphs
<LeadText>Główny opis sekcji</LeadText>
```

### Hierarchia nagłówków
- `HeroTitle` - H1, główny tytuł strony
- `SectionTitle` - H2, tytuły sekcji
- `CardTitle` - H3, tytuły kart/komponentów
- `SubTitle` - H4, podtytuły

---

## 🎯 Ikony

### Unified Icon System
```jsx
import { Icon } from '@/components/ui/IconSystem';

// Podstawowe użycie
<Icon name="heart" size="md" color="accent" />

// Rozmiary: xs, sm, md, lg, xl, 2xl, 3xl
<Icon name="star" size="lg" />

// Kolory: primary, secondary, accent, muted, light, white
<Icon name="check" color="success" />

// Wyspecjalizowane komponenty
<NavigationIcon name="menu" />
<SocialIcon name="instagram" />
<ActionIcon name="edit" />
```

### Dostępne ikony
```jsx
// Nawigacja
'menu', 'close', 'chevron-down', 'arrow-right', 'home'

// Social media
'mail', 'phone', 'instagram', 'facebook'

// Lokalizacja
'map-pin', 'globe', 'compass'

// Czas
'calendar', 'clock', 'sunrise', 'sunset'

// Użytkownicy
'user', 'users', 'heart', 'star'

// Natura
'mountain', 'waves', 'flower', 'sparkles'

// Akcje
'check', 'edit', 'trash', 'save'
```

---

## 🧩 Komponenty

### Przyciski
```jsx
import { 
  UnifiedButton, 
  CTAButton, 
  SecondaryButton, 
  GhostButton 
} from '@/components/ui/UnifiedButton';

// Główne akcje
<CTAButton>Zarezerwuj teraz</CTAButton>

// Drugie w hierarchii
<SecondaryButton>Dowiedz się więcej</SecondaryButton>

// Subtelne akcje
<GhostButton>Anuluj</GhostButton>
```

### Karty
```jsx
import { 
  UnifiedCard, 
  ServiceCard, 
  TestimonialCard 
} from '@/components/ui/UnifiedCard';

// Podstawowa karta
<UnifiedCard>
  <CardContent>Treść karty</CardContent>
</UnifiedCard>

// Karta usługi
<ServiceCard>
  <CardTitle>Retreat na Bali</CardTitle>
  <BodyText>Opis retreatu...</BodyText>
</ServiceCard>
```

### Blog Components
```jsx
import { 
  InfoBox, 
  WarningBox, 
  SuccessBox, 
  CTABox 
} from '@/components/ui/BlogComponents';

// Informacje
<InfoBox title="Ważne informacje">
  Treść informacji...
</InfoBox>

// Ostrzeżenia
<WarningBox title="Uwaga!">
  Treść ostrzeżenia...
</WarningBox>

// Call to Action
<CTABox 
  title="Gotowy na przygodę?"
  description="Zarezerwuj swój retreat już dziś"
  buttonText="Zarezerwuj"
  buttonHref="/rezerwacja"
/>
```

---

## 💻 Zasady kodowania

### Import order
```jsx
// 1. React i Next.js
import React from 'react';
import Link from 'next/link';

// 2. UI Components
import { Icon } from '@/components/ui/IconSystem';
import { UnifiedButton } from '@/components/ui/UnifiedButton';

// 3. Custom components
import MinimalistHero from '@/components/MinimalistHero';

// 4. Data i utils
import { contactData } from '@/data/contactData';
```

### Naming conventions
```jsx
// ✅ Komponenty - PascalCase
const RetreatCard = () => {};

// ✅ Funkcje - camelCase
const handleBooking = () => {};

// ✅ Stałe - UPPER_SNAKE_CASE
const API_ENDPOINTS = {};

// ✅ CSS classes - kebab-case lub Tailwind
className="retreat-card bg-sanctuary text-charcoal"
```

### Responsive design
```jsx
// ✅ Mobile-first approach
className="text-sm md:text-base lg:text-lg"

// ✅ Consistent breakpoints
// sm: 640px, md: 768px, lg: 1024px, xl: 1280px
```

---

## 🔄 Migracja

### Mapowanie starych kolorów
```jsx
// Stare → Nowe
'temple' → 'charcoal'
'temple-gold' → 'enterprise-brown'
'golden' → 'terra'
'rice' → 'sanctuary'
'wood-light' → 'charcoal-light'
```

### Migracja ikon
```jsx
// Stare (Heroicons/Lucide mixed)
import { MapPinIcon } from '@heroicons/react/24/outline';
import { MapPin } from 'lucide-react';

// Nowe (Unified)
import { Icon } from '@/components/ui/IconSystem';
<Icon name="map-pin" size="md" color="accent" />
```

### Migracja przycisków
```jsx
// Stare
<button className="bg-temple text-white px-4 py-2 rounded">
  Kliknij
</button>

// Nowe
<UnifiedButton variant="primary" size="md">
  Kliknij
</UnifiedButton>
```

---

## 🎯 Checklist przed commitem

- [ ] Używam unified color system (charcoal, enterprise-brown, etc.)
- [ ] Używam Icon component zamiast bezpośrednich importów
- [ ] Używam UnifiedButton zamiast custom button styles
- [ ] Używam typography components (HeroTitle, SectionTitle, etc.)
- [ ] Kod jest responsive (mobile-first)
- [ ] Import order jest poprawny
- [ ] Naming conventions są zachowane

---

## 🚀 Performance

### Lazy loading
```jsx
// ✅ Lazy load heavy components
const HeavyComponent = lazy(() => import('./HeavyComponent'));

// ✅ Optimize images
<OptimizedImage 
  src="/retreat-bali.jpg" 
  alt="Retreat na Bali"
  width={800}
  height={600}
  priority={false}
/>
```

### Bundle optimization
- Używaj tree-shaking friendly imports
- Unikaj importowania całych bibliotek
- Używaj dynamic imports dla route-specific code

---

## 📞 Kontakt

W przypadku pytań dotyczących design system:
- Sprawdź dokumentację w `/src/components/ui/`
- Przejrzyj przykłady w głównych stronach
- Skonsultuj się z zespołem przed wprowadzeniem zmian

---

**Ostatnia aktualizacja:** 2024-12-19
**Wersja:** 2.0.0 - Unified Design System