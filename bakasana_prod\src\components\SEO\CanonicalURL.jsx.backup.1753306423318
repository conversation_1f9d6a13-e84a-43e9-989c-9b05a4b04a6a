'use client';

import { usePathname } from 'next/navigation';
import Head from 'next/head';

const CanonicalURL = ({ customCanonical = null }) => {
  const pathname = usePathname();
  
  // Base URL strony
  const baseURL = 'https://flywithbakasana.pl';
  
  // <PERSON><PERSON><PERSON> custom canonical, uż<PERSON>j go
  const canonicalURL = customCanonical || `${baseURL}${pathname}`;
  
  // Usuń trailing slash jeśli nie jest to root
  const cleanCanonicalURL = canonicalURL === baseURL + '/' ? baseURL : canonicalURL.replace(/\/$/, '');

  return (
    <Head>
      <link rel="canonical" href={cleanCanonicalURL} />
    </Head>
  );
};

export default CanonicalURL;