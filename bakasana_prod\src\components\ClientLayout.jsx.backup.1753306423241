// src/components/ClientLayout.jsx
"use client";
import dynamic from 'next/dynamic';
import { Suspense } from 'react';
import ErrorBoundary from './ErrorBoundary';

const Navbar = dynamic(() => import('@/components/Navbar'), {
  ssr: false,
  loading: () => <div className="h-16 md:h-20" />
});

const Footer = dynamic(() => import('@/components/Footer'), {
  ssr: true,
  loading: () => <div className="h-20" />
});

export function ClientLayout({ children }) {
  return (
    <ErrorBoundary>
      <Suspense fallback={<div className="h-16 md:h-20" />}>
        <Navbar />
      </Suspense>
      <main role="main" className="flex-grow mb-lg md:mb-xl">
        {children}
      </main>
      <Suspense fallback={<div className="h-20" />}>
        <Footer />
      </Suspense>
    </ErrorBoundary>
  );
}