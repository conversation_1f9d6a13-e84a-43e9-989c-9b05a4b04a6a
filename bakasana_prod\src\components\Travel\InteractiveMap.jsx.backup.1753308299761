import React, { useState, useEffect, useRef } from 'react';

import { Icon   } from '@/components/ui/IconSystem';
import { Icon   } from '@/components/ui/IconSystem';
import { MapPinIcon, PhotoIcon, InformationCircleIcon   } from '@/components/ui/UnifiedButton';
import { UnifiedButton  } from '@/components/ui/UnifiedButton';

export default InteractiveMap;

'use client';


const InteractiveMap = ({ destinations = [], className = '' }) => {
  const [selectedDestination, setSelectedDestination] = useState(null);
  const [hoveredDestination, setHoveredDestination] = useState(null);
  const mapRef = useRef(null);

  // Default destinations for Bali & Sri Lanka
  const defaultDestinations = [
    {
      id: 'ubud',
      name: 'Ubud',
      country: 'Bali, Indonesia',
      coordinates: { lat: -8.5069, lng: 115.2625 },
      description: 'Spiritual heart of Bali with rice terraces and temples',
      image: '/images/destinations/ubud.webp',
      highlights: ['Rice terraces', 'Ancient temples', 'Yoga studios', 'Art galleries'],
      climate: 'Tropical, 24-30°C',
      bestTime: 'April-September'
    },
    {
      id: 'gili-air',
      name: 'Gili Air',
      country: 'Lombok, Indonesia',
      coordinates: { lat: -8.3572, lng: 116.0858 },
      description: 'Peaceful island paradise with pristine beaches',
      image: '/images/destinations/gili-air.webp',
      highlights: ['White sand beaches', 'Crystal clear water', 'No cars', 'Snorkeling'],
      climate: 'Tropical, 26-32°C',
      bestTime: 'May-October'
    },
    {
      id: 'southern-sri-lanka',
      name: 'Southern Coast',
      country: 'Sri Lanka',
      coordinates: { lat: 6.0535, lng: 80.2210 },
      description: 'Pristine beaches and ancient Buddhist heritage',
      image: '/images/destinations/sri-lanka-south.webp',
      highlights: ['Golden beaches', 'Buddhist temples', 'Whale watching', 'Ayurveda'],
      climate: 'Tropical, 22-30°C',
      bestTime: 'November-April'
    },
    {
      id: 'kandy',
      name: 'Kandy',
      country: 'Sri Lanka',
      coordinates: { lat: 7.2906, lng: 80.6337 },
      description: 'Cultural capital with sacred temples and lush hills',
      image: '/images/destinations/kandy.webp',
      highlights: ['Temple of the Tooth', 'Royal Botanical Gardens', 'Traditional dance', 'Tea plantations'],
      climate: 'Tropical highland, 18-28°C',
      bestTime: 'December-April'
    }
  ];

  const allDestinations = destinations.length > 0 ? destinations : defaultDestinations;

  useEffect(() => {
    // Initialize map when component mounts
    if (typeof window !== 'undefined' && mapRef.current) {
      // Map initialization logic would go here
      // For now, we'll use a static image approach
    }
  }, []);

  const handleDestinationClick = (destination) => {
    setSelectedDestination(destination);
  };

  const handleDestinationHover = (destination) => {
    setHoveredDestination(destination);
  };

  const handleDestinationLeave = () => {
    setHoveredDestination(null);
  };

  return (
    <div className={`relative w-full h-96 bg-ocean-blue/5  overflow-hidden ${className}`}>
      {/* Map Container */}
      <div ref={mapRef} className="relative w-full h-full">
        {/* Static Map Background */}
        <div className="absolute inset-0 bg-gradient-to-b from-ocean-blue/10 to-sage-green/10">
          <div className="absolute inset-0 bg-[url('/images/background/map-pattern.svg')] opacity-5" />
        </div>

        {/* Interactive Pins */}
        {allDestinations.map((destination) => (
          <div
            key={destination.id}
            className={`
              absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer
              transition-all duration-300 z-10
              ${hoveredDestination?.id === destination.id ? 'scale-125' : 'scale-100'}
              ${selectedDestination?.id === destination.id ? 'z-20' : ''}
            `}
            style={{
              left: `${50 + (destination.coordinates.lng - 100) * 2}%`,
              top: `${50 + (destination.coordinates.lat + 2) * 5}%`
            }}
            onClick={() => handleDestinationClick(destination)}
            onMouseEnter={() => handleDestinationHover(destination)}
            onMouseLeave={handleDestinationLeave}
          >
            {/* Pin */}
            <div className={`
              relative w-6 h-6 rectangular border-2 border-white shadow-lg
              ${selectedDestination?.id === destination.id 
                ? 'bg-charcoal-gold' 
                : 'bg-sage-green'
              }
              ${hoveredDestination?.id === destination.id 
                ? 'animate-pulse' 
                : ''
              }
            `}>
              <Icon name="map-pin" size="md" color="primary" />
            </div>

            {/* Tooltip */}
            {hoveredDestination?.id === destination.id && (
              <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-white p-3 shadow-lg w-48 z-30">
                <h4 className="font-cormorant font-medium text-charcoal mb-1">
                  {destination.name}
                </h4>
                <p className="text-sm text-stone mb-2">
                  {destination.country}
                </p>
                <p className="text-xs text-stone line-clamp-2">
                  {destination.description}
                </p>
              </div>
            )}
          </div>
        ))}

        {/* Destination Info Panel */}
        {selectedDestination && (
          <div className="absolute bottom-0 left-0 right-0 bg-white p-6 border-t border-stone-light">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-md">
              <div>
                <h3 className="font-cormorant text-xl font-medium text-charcoal mb-2 /* TODO: Replace with CardTitle */">
                  {selectedDestination.name}
                </h3>
                <p className="text-stone text-sm mb-3">
                  {selectedDestination.country}
                </p>
                <p className="text-charcoal text-sm line-clamp-3">
                  {selectedDestination.description}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-charcoal mb-2">Highlights</h4>
                <div className="flex flex-wrap gap-2 mb-3">
                  {selectedDestination.highlights.map((highlight, index) => (
                    <span
                      key={index}
                      className="text-xs bg-sanctuary text-stone px-2 py-1 rectangular"
                    >
                      {highlight}
                    </span>
                  ))}
                </div>
                
                <div className="grid grid-cols-2 gap-sm text-sm">
                  <div>
                    <span className="text-stone">Climate:</span>
                    <p className="text-charcoal">{selectedDestination.climate}</p>
                  </div>
                  <div>
                    <span className="text-stone">Best time:</span>
                    <p className="text-charcoal">{selectedDestination.bestTime}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <button
              onClick={() => setSelectedDestination(null)}
              className="absolute top-4 right-4 text-stone hover:text-charcoal"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="absolute top-4 left-4 bg-white p-3 shadow-lg">
        <h4 className="font-medium text-charcoal mb-2 text-sm">Destinations</h4>
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-sage-green rectangular"></div>
            <span className="text-xs text-stone">Available</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-charcoal-gold rectangular"></div>
            <span className="text-xs text-stone">Selected</span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="absolute top-4 right-4 flex gap-2">
        <UnifiedButton variant="primary" size="md">
          <PhotoIcon className="h-5 w-5 text-stone" />
        </UnifiedButton>
        <UnifiedButton variant="primary" size="md">
          <InformationCircleIcon className="h-5 w-5 text-stone" />
        </UnifiedButton>
      </div>
    </div>
  );
};

