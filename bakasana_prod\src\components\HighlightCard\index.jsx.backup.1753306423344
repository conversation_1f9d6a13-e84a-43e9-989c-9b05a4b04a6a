'use client';

import React from 'react';

import { HeroTitle, SectionTitle, CardTitle, BodyText } from '@/components/ui/UnifiedTypography';

const HighlightCard = ({ title, description, icon: Icon, index = 0 }) => {
  return (
    <article className="unified-card p-8 group animate-gentle" style={{ animationDelay: `${index * 100}ms` }}>
      <div className="flex items-center gap-sm mb-sm">
        <div className="w-10 h-0.5 bg-accent/20"></div>
        <CardTitle>{title}</CardTitle>
      </div>
      <div className="flex gap-3">
        <Icon className="w-5 h-5 text-accent/60 mt-0.5 transition-transform duration-300 group-hover:scale-110" />
        <p className="text-primary leading-relaxed font-light">{description}</p>
      </div>
    </article>
  );
};

export default HighlightCard;