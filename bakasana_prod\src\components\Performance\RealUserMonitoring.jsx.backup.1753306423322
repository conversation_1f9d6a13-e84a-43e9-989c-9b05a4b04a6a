'use client';

import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';

export default function RealUserMonitoring() {
  const pathname = usePathname();
  const sessionRef = useRef({
    startTime: Date.now(),
    interactions: 0,
    clicks: 0,
    scrollDepth: 0,
    timeSpent: 0,
    bounceThreshold: 30000, // 30 seconds
    isEngaged: false,
    errors: [],
    performance: {},
  });

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') {
      return;
    }

    const session = sessionRef.current;
    session.startTime = Date.now();
    session.interactions = 0;
    session.clicks = 0;
    session.scrollDepth = 0;
    session.isEngaged = false;
    session.errors = [];

    // Track Core Web Vitals with detailed context
    const trackWebVital = (metric) => {
      const vitalsData = {
        name: metric.name,
        value: metric.value,
        rating: metric.rating,
        delta: metric.delta,
        id: metric.id,
        page: pathname,
        timestamp: Date.now(),
        connection: navigator.connection?.effectiveType || 'unknown',
        deviceMemory: navigator.deviceMemory || 'unknown',
        hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        screen: {
          width: screen.width,
          height: screen.height,
        },
        userAgent: navigator.userAgent,
        referrer: document.referrer,
        sessionId: session.startTime,
      };

      // Send to multiple analytics platforms
      if (window.gtag) {
        window.gtag('event', 'web_vital', {
          event_category: 'Web Vitals',
          event_label: `${metric.name}_${metric.rating}`,
          value: Math.round(metric.value),
          custom_parameter_1: pathname,
          custom_parameter_2: metric.rating,
        });
      }

      if (window.mixpanel) {
        window.mixpanel.track('Web Vital', vitalsData);
      }

      // Track performance thresholds
      const thresholds = {
        CLS: { good: 0.1, poor: 0.25 },
        FID: { good: 100, poor: 300 },
        FCP: { good: 1800, poor: 3000 },
        LCP: { good: 2500, poor: 4000 },
        TTFB: { good: 800, poor: 1800 },
        INP: { good: 200, poor: 500 },
      };

      const threshold = thresholds[metric.name];
      if (threshold) {
        let performanceRating = 'good';
        if (metric.value > threshold.poor) {
          performanceRating = 'poor';
        } else if (metric.value > threshold.good) {
          performanceRating = 'needs-improvement';
        }

        if (performanceRating === 'poor') {
          // Alert for poor performance
          console.warn(`Poor ${metric.name} performance detected:`, metric.value);
          
          if (window.Sentry) {
            window.Sentry.captureMessage(`Poor ${metric.name} performance`, {
              level: 'warning',
              extra: vitalsData,
            });
          }
        }
      }

      session.performance[metric.name] = vitalsData;
    };

    // Initialize Web Vitals tracking with error handling
    import('web-vitals').then(({ 
      getCLS, getFID, getFCP, getLCP, getTTFB, getINP 
    }) => {
      try {
        getCLS(trackWebVital);
        getFID(trackWebVital);
        getFCP(trackWebVital);
        getLCP(trackWebVital);
        getTTFB(trackWebVital);
        getINP(trackWebVital);
      } catch (error) {
        console.warn('Web Vitals tracking failed:', error);
      }
    }).catch(error => {
      console.warn('Failed to load web-vitals:', error);
    });

    // Track detailed user interactions
    const trackInteraction = (event) => {
      session.interactions++;
      
      if (event.type === 'click') {
        session.clicks++;
        
        const target = event.target;
        const tagName = target.tagName.toLowerCase();
        const classList = Array.from(target.classList).join(' ');
        const textContent = target.textContent?.trim().substring(0, 50) || '';
        const href = target.href || target.closest('a')?.href;
        
        const interactionData = {
          type: 'click',
          element: tagName,
          classes: classList,
          text: textContent,
          href: href,
          page: pathname,
          timestamp: Date.now(),
          sessionId: session.startTime,
          x: event.clientX,
          y: event.clientY,
        };

        if (window.mixpanel) {
          window.mixpanel.track('User Interaction', interactionData);
        }

        // Track specific business events
        if (href) {
          if (href.includes('rezerwacja') || href.includes('booking')) {
            trackBookingFunnel('click', { element: tagName, text: textContent });
          } else if (href.includes('program')) {
            trackRetreatInterest('click', { element: tagName, text: textContent });
          } else if (href.includes('kontakt')) {
            trackContactInterest('click', { element: tagName, text: textContent });
          }
        }
      }
    };

    // Track scroll behavior
    const trackScroll = () => {
      const scrollY = window.pageYOffset;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      const scrollPercent = Math.round((scrollY / (documentHeight - windowHeight)) * 100);
      
      if (scrollPercent > session.scrollDepth) {
        session.scrollDepth = scrollPercent;
        
        // Track scroll milestones
        if (scrollPercent >= 25 && scrollPercent % 25 === 0) {
          if (window.gtag) {
            window.gtag('event', 'scroll_milestone', {
              event_category: 'Engagement',
              event_label: `${scrollPercent}%`,
              value: scrollPercent,
              custom_parameter_1: pathname,
            });
          }
        }
      }
    };

    // Track time spent and engagement
    const trackEngagement = () => {
      session.timeSpent = Date.now() - session.startTime;
      
      // Mark as engaged after 30 seconds
      if (session.timeSpent > session.bounceThreshold && !session.isEngaged) {
        session.isEngaged = true;
        
        if (window.gtag) {
          window.gtag('event', 'engaged_user', {
            event_category: 'Engagement',
            event_label: pathname,
            value: Math.round(session.timeSpent / 1000),
            custom_parameter_1: session.interactions,
            custom_parameter_2: session.scrollDepth,
          });
        }

        if (window.mixpanel) {
          window.mixpanel.track('User Engaged', {
            page: pathname,
            time_to_engagement: session.timeSpent,
            interactions: session.interactions,
            scroll_depth: session.scrollDepth,
            timestamp: Date.now(),
          });
        }
      }
    };

    // Track errors
    const trackError = (error) => {
      const errorData = {
        message: error.message,
        filename: error.filename,
        lineno: error.lineno,
        colno: error.colno,
        stack: error.error?.stack,
        page: pathname,
        timestamp: Date.now(),
        sessionId: session.startTime,
        userAgent: navigator.userAgent,
      };

      session.errors.push(errorData);

      if (window.gtag) {
        window.gtag('event', 'exception', {
          description: error.message,
          fatal: false,
          event_category: 'JavaScript Error',
          event_label: pathname,
        });
      }

      if (window.mixpanel) {
        window.mixpanel.track('JavaScript Error', errorData);
      }
    };

    // Track resource loading performance
    const trackResourceTiming = () => {
      const entries = performance.getEntriesByType('resource');
      const slowResources = entries.filter(entry => entry.duration > 1000);
      
      slowResources.forEach(resource => {
        const resourceData = {
          name: resource.name,
          type: resource.initiatorType,
          duration: Math.round(resource.duration),
          size: resource.transferSize,
          page: pathname,
          timestamp: Date.now(),
        };

        if (window.mixpanel) {
          window.mixpanel.track('Slow Resource', resourceData);
        }
      });
    };

    // Track memory usage
    const trackMemoryUsage = () => {
      if (performance.memory) {
        const memoryData = {
          usedJSHeapSize: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
          totalJSHeapSize: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
          jsHeapSizeLimit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024),
          page: pathname,
          timestamp: Date.now(),
        };

        if (window.mixpanel) {
          window.mixpanel.track('Memory Usage', memoryData);
        }
      }
    };

    // Business-specific tracking functions
    const trackBookingFunnel = (action, data) => {
      if (window.gtag) {
        window.gtag('event', 'booking_funnel', {
          event_category: 'Conversion',
          event_label: action,
          value: 1,
          custom_parameter_1: data.element,
          custom_parameter_2: data.text,
        });
      }
    };

    const trackRetreatInterest = (action, data) => {
      if (window.gtag) {
        window.gtag('event', 'retreat_interest', {
          event_category: 'Interest',
          event_label: action,
          value: 1,
          custom_parameter_1: data.element,
          custom_parameter_2: data.text,
        });
      }
    };

    const trackContactInterest = (action, data) => {
      if (window.gtag) {
        window.gtag('event', 'contact_interest', {
          event_category: 'Lead',
          event_label: action,
          value: 1,
          custom_parameter_1: data.element,
          custom_parameter_2: data.text,
        });
      }
    };

    // Set up event listeners
    document.addEventListener('click', trackInteraction);
    document.addEventListener('keydown', trackInteraction);
    window.addEventListener('scroll', trackScroll, { passive: true });
    window.addEventListener('error', trackError);
    
    // Set up intervals
    const engagementInterval = setInterval(trackEngagement, 5000);
    const resourceInterval = setInterval(trackResourceTiming, 30000);
    const memoryInterval = setInterval(trackMemoryUsage, 60000);

    // Track page visibility changes
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Track when user leaves page
        const sessionData = {
          ...session,
          timeSpent: Date.now() - session.startTime,
          page: pathname,
          timestamp: Date.now(),
          isBounce: !session.isEngaged,
        };

        if (window.mixpanel) {
          window.mixpanel.track('Page Exit', sessionData);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup
    return () => {
      document.removeEventListener('click', trackInteraction);
      document.removeEventListener('keydown', trackInteraction);
      window.removeEventListener('scroll', trackScroll);
      window.removeEventListener('error', trackError);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(engagementInterval);
      clearInterval(resourceInterval);
      clearInterval(memoryInterval);
    };
  }, [pathname]);

  return null;
}