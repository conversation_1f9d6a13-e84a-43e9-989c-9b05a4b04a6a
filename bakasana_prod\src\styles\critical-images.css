
/* Critical Image Preloading - Generated by BAKASANA optimizer */
.hero-bg {
  background-image: url('/images/background/bali-hero.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Fallback for browsers without WebP support */
.no-webp .hero-bg {
  background-image: url('/images/background/bali-hero.jpg');
}

/* Lazy loading placeholder */
.lazy-image {
  background-color: #f0f0f0;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjRjBGMEYwIi8+CjxwYXRoIGQ9Ik0xMCA3QzguMzQzMTUgNyA3IDguMzQzMTUgNyAxMEM3IDExLjY1NjkgOC4zNDMxNSAxMyAxMCAxM0MxMS42NTY5IDEzIDEzIDExLjY1NjkgMTMgMTBDMTMgOC4zNDMxNSAxMS42NTY5IDcgMTAgN1oiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+');
  background-repeat: no-repeat;
  background-position: center;
  transition: opacity 0.3s ease;
}

.lazy-image.loaded {
  background-image: none;
}
