#!/usr/bin/env node

/**
 * BAKASANA - STYLE CONSISTENCY CHECKER
 * Sprawdza konsekwentność stylistyczną w całej aplikacji
 */

const fs = require('fs');
const path = require('path');
const FileProcessor = require('./utils/FileProcessor');
const Logger = require('./utils/Logger');

class StyleChecker extends FileProcessor {
  constructor() {
    super(path.join(__dirname, '..'));
    this.logger = new Logger('StyleChecker', { colors: true });
    this.issues = [];
    this.stats = {
      totalFiles: 0,
      checkedFiles: 0,
      issues: 0,
      score: 0
    };
  }

  log(message, type = 'info') {
    switch(type) {
      case 'success':
        this.logger.success(message);
        break;
      case 'warning':
        this.logger.warn(message);
        break;
      case 'error':
        this.logger.error(message);
        break;
      default:
        this.logger.info(message);
    }
  }

  addIssue(file, line, type, message, suggestion = null) {
    this.issues.push({
      file: path.relative(this.srcPath, file),
      line,
      type,
      message,
      suggestion
    });
  }

  checkColors(file, content) {
    const lines = content.split('\n');
    
    // Legacy colors that should be migrated
    const legacyColors = [
      'text-temple', 'bg-temple', 'border-temple',
      'text-temple-gold', 'bg-temple-gold', 'border-temple-gold',
      'text-golden', 'bg-golden', 'border-golden',
      'text-wood-light', 'bg-wood-light',
      'bg-rice', 'bg-mist', 'bg-shell'
    ];

    // System colors that should use brand colors
    const systemColors = [
      'bg-yellow-50', 'bg-red-50', 'bg-blue-50', 'bg-green-50', 'bg-purple-50',
      'text-yellow-800', 'text-red-800', 'text-blue-800', 'text-green-800',
      'border-yellow-200', 'border-red-200', 'border-blue-200', 'border-green-200'
    ];

    lines.forEach((line, index) => {
      // Check for legacy colors
      legacyColors.forEach(color => {
        if (line.includes(color)) {
          const suggestion = this.getLegacyColorSuggestion(color);
          this.addIssue(
            file, 
            index + 1, 
            'legacy-color', 
            `Legacy color "${color}" found`,
            `Replace with "${suggestion}"`
          );
        }
      });

      // Check for system colors in blog posts
      if (file.includes('/blog/')) {
        systemColors.forEach(color => {
          if (line.includes(color)) {
            this.addIssue(
              file,
              index + 1,
              'system-color',
              `System color "${color}" in blog post`,
              'Use BlogComponents (InfoBox, WarningBox, etc.) instead'
            );
          }
        });
      }
    });
  }

  checkIcons(file, content) {
    const lines = content.split('\n');

    // Check for mixed icon libraries
    const hasHeroicons = content.includes('@heroicons/react');
    const hasLucide = content.includes('lucide-react');
    const hasUnifiedIcons = content.includes("from '@/components/ui/IconSystem'");

    if ((hasHeroicons || hasLucide) && !hasUnifiedIcons) {
      this.addIssue(
        file,
        1,
        'icon-system',
        'File uses legacy icon imports',
        'Migrate to unified Icon system'
      );
    }

    // Check for hardcoded icon sizes and colors
    lines.forEach((line, index) => {
      const iconPattern = /<\w+Icon\s+className="([^"]*)"|\w+\s+className="([^"]*w-\d+[^"]*)".*\/>/;
      const match = line.match(iconPattern);
      
      if (match) {
        const className = match[1] || match[2];
        if (className && (className.includes('w-') || className.includes('h-'))) {
          this.addIssue(
            file,
            index + 1,
            'hardcoded-icon',
            'Hardcoded icon size/color found',
            'Use <Icon name="..." size="md" color="accent" /> instead'
          );
        }
      }
    });
  }

  checkButtons(file, content) {
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      // Check for hardcoded button styles
      if (line.includes('<button') && line.includes('className=')) {
        const hasUnifiedButton = content.includes('UnifiedButton');
        if (!hasUnifiedButton) {
          this.addIssue(
            file,
            index + 1,
            'hardcoded-button',
            'Hardcoded button styles found',
            'Use UnifiedButton component instead'
          );
        }
      }

      // Check for rounded buttons (should be rectangular in BAKASANA)
      if (line.includes('rounded-full') || line.includes('rounded-lg')) {
        this.addIssue(
          file,
          index + 1,
          'rounded-elements',
          'Rounded elements found',
          'BAKASANA uses rectangular design (border-radius: 0)'
        );
      }
    });
  }

  checkTypography(file, content) {
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      // Check for hardcoded headings
      const headingPattern = /<h[1-6][^>]*className="[^"]*text-\d+xl/;
      if (headingPattern.test(line)) {
        const hasUnifiedTypography = content.includes('UnifiedTypography');
        if (!hasUnifiedTypography) {
          this.addIssue(
            file,
            index + 1,
            'hardcoded-typography',
            'Hardcoded heading styles found',
            'Use HeroTitle, SectionTitle, or CardTitle components'
          );
        }
      }

      // Check for inconsistent font families
      if (line.includes('font-serif') && !line.includes('font-cormorant')) {
        this.addIssue(
          file,
          index + 1,
          'font-family',
          'Generic font-serif found',
          'Use font-cormorant for headings'
        );
      }
    });
  }

  checkImports(file, content) {
    const lines = content.split('\n');
    const imports = lines.filter(line => line.trim().startsWith('import'));

    // Check import order
    let lastImportType = 0; // 1: React/Next, 2: UI, 3: Custom, 4: Data
    
    imports.forEach((line, index) => {
      let currentType = 4; // Default to data/utils
      
      if (line.includes('react') || line.includes('next/')) {
        currentType = 1;
      } else if (line.includes('@/components/ui/')) {
        currentType = 2;
      } else if (line.includes('@/components/')) {
        currentType = 3;
      }

      if (currentType < lastImportType) {
        this.addIssue(
          file,
          index + 1,
          'import-order',
          'Imports are not in correct order',
          'Order: React/Next → UI Components → Custom Components → Data/Utils'
        );
      }
      
      lastImportType = Math.max(lastImportType, currentType);
    });
  }

  getLegacyColorSuggestion(color) {
    const mapping = {
      'text-temple': 'text-charcoal',
      'bg-temple': 'bg-charcoal',
      'border-temple': 'border-charcoal',
      'text-temple-gold': 'text-enterprise-brown',
      'bg-temple-gold': 'bg-enterprise-brown',
      'border-temple-gold': 'border-enterprise-brown',
      'text-golden': 'text-terra',
      'bg-golden': 'bg-terra',
      'border-golden': 'border-terra',
      'text-wood-light': 'text-charcoal-light',
      'bg-rice': 'bg-sanctuary',
      'bg-mist': 'bg-linen',
      'bg-shell': 'bg-silk'
    };
    
    return mapping[color] || 'unified-color';
  }

  getAllFiles(dir, extensions) {
    let files = [];
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files = files.concat(this.getAllFiles(fullPath, extensions));
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
    
    return files;
  }

  async run() {
    this.log('🔍 Sprawdzanie konsekwentności stylów...', 'info');
    
    const files = this.getAllFiles(this.srcPath, ['.jsx', '.tsx', '.js', '.ts']);
    this.stats.totalFiles = files.length;

    for (const file of files) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        this.stats.checkedFiles++;

        this.checkColors(file, content);
        this.checkIcons(file, content);
        this.checkButtons(file, content);
        this.checkTypography(file, content);
        this.checkImports(file, content);

      } catch (error) {
        this.log(`Error checking ${file}: ${error.message}`, 'error');
      }
    }

    this.generateReport();
  }

  generateReport() {
    this.stats.issues = this.issues.length;
    
    // Calculate score (100 - issues per 10 files)
    const issuesPerFile = this.stats.issues / this.stats.checkedFiles;
    this.stats.score = Math.max(0, Math.round(100 - (issuesPerFile * 50)));

    this.log('\n📊 RAPORT KONSEKWENTNOŚCI STYLÓW', 'info');
    this.log('═'.repeat(50), 'info');
    
    // Score with color
    const scoreColor = this.stats.score >= 90 ? 'success' : 
                      this.stats.score >= 70 ? 'warning' : 'error';
    this.log(`🎯 Ocena: ${this.stats.score}/100`, scoreColor);
    
    this.log(`📁 Sprawdzone pliki: ${this.stats.checkedFiles}/${this.stats.totalFiles}`, 'info');
    this.log(`⚠️  Znalezione problemy: ${this.stats.issues}`, this.stats.issues > 0 ? 'warning' : 'success');

    if (this.issues.length > 0) {
      this.log('\n🔧 PROBLEMY DO NAPRAWIENIA:', 'warning');
      
      // Group issues by type
      const groupedIssues = {};
      this.issues.forEach(issue => {
        if (!groupedIssues[issue.type]) {
          groupedIssues[issue.type] = [];
        }
        groupedIssues[issue.type].push(issue);
      });

      Object.entries(groupedIssues).forEach(([type, issues]) => {
        this.log(`\n📋 ${type.toUpperCase()} (${issues.length} problemów):`, 'warning');
        
        issues.slice(0, 5).forEach(issue => { // Show max 5 per type
          this.log(`  📄 ${issue.file}:${issue.line}`, 'info');
          this.log(`     ❌ ${issue.message}`, 'error');
          if (issue.suggestion) {
            this.log(`     💡 ${issue.suggestion}`, 'success');
          }
        });

        if (issues.length > 5) {
          this.log(`     ... i ${issues.length - 5} więcej`, 'info');
        }
      });

      this.log('\n🚀 NASTĘPNE KROKI:', 'info');
      this.log('1. Uruchom: npm run migrate:unified', 'info');
      this.log('2. Sprawdź STYLE_GUIDE.md', 'info');
      this.log('3. Popraw pozostałe problemy ręcznie', 'info');
    } else {
      this.log('\n🎉 Gratulacje! Wszystkie style są konsekwentne!', 'success');
    }

    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      stats: this.stats,
      issues: this.issues,
      summary: {
        score: this.stats.score,
        totalIssues: this.stats.issues,
        issuesByType: {}
      }
    };

    // Count issues by type
    this.issues.forEach(issue => {
      if (!report.summary.issuesByType[issue.type]) {
        report.summary.issuesByType[issue.type] = 0;
      }
      report.summary.issuesByType[issue.type]++;
    });

    fs.writeFileSync(
      path.join(__dirname, '../style-check-report.json'),
      JSON.stringify(report, null, 2)
    );

    this.log('\n📄 Szczegółowy raport zapisany w style-check-report.json', 'info');
    
    // Exit with error code if score is too low
    if (this.stats.score < 70) {
      this.log('\n❌ Ocena poniżej 70/100 - wymagane poprawki!', 'error');
      process.exit(1);
    }
  }
}

// Run checker
if (require.main === module) {
  const checker = new StyleChecker();
  checker.run();
}

module.exports = StyleChecker;