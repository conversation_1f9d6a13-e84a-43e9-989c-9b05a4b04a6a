import Head from 'next/head';

import { generateEnterpriseMetadata } from '@/lib/enterpriseSEO';

export default function EnterpriseMetaTags({ 
  page = '/', 
  data = {}, 
  customMetadata = {},
  userLocation = null,
  device = 'desktop',
  timestamp = new Date().toISOString()
}) {
  
  // Generate enterprise-level metadata
  const metadata = generateEnterpriseMetadata({
    page,
    data: { ...data, ...customMetadata },
    userLocation,
    device,
    timestamp
  });

  // Advanced robots directives
  const robotsDirectives = [
    'index',
    'follow',
    'max-image-preview:large',
    'max-snippet:-1',
    'max-video-preview:-1',
    'noarchive',
    'notranslate',
    'noimageindex'
  ].join(', ');

  // Advanced structured data
  const structuredDataScript = `
    ${JSON.stringify(metadata.structuredData, null, 0)}
  `;

  // Critical CSS for above-the-fold content
  const criticalCSS = `
    /* Critical CSS for instant rendering */
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { 
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: #FDFCF8;
      color: #3A3A3A;
      line-height: 1.6;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    .hero {
      height: 100vh;
      min-height: 600px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #FDFCF8 0%, #F9F7F2 50%, #F5F3EF 100%);
      position: relative;
      overflow: hidden;
    }
    .navigation {
      position: fixed;
      top: 0;
      width: 100%;
      z-index: 100;
      background: rgba(253, 252, 248, 0.95);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(201, 165, 117, 0.1);
    }
    .hero-title {
      font-size: clamp(2.5rem, 5vw, 4rem);
      font-weight: 300;
      text-align: center;
      margin-bottom: 2rem;
      color: #2C2C2C;
      line-height: 1.2;
    }
    .hero-subtitle {
      font-size: clamp(1.2rem, 2.5vw, 1.8rem);
      text-align: center;
      margin-bottom: 3rem;
      color: #666;
      font-weight: 300;
    }
    .cta-button {
      display: inline-block;
      padding: 1rem 2rem;
      background: linear-gradient(135deg, #C9A575 0%, #B8956A 100%);
      color: white;
      text-decoration: none;
      
      transition: all 0.3s ease;
      font-weight: 500;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 15px rgba(201, 165, 117, 0.3);
    }
    .cta-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(201, 165, 117, 0.4);
    }
    @media (max-width: 768px) {
      .hero { min-height: 500px; }
      .hero-title { font-size: 2rem; }
      .hero-subtitle { font-size: 1.1rem; }
    }
  `;

  return (
    <Head>
      {/* ========================================
          BASIC META TAGS
      ======================================== */}
      <title>{metadata.title}</title>
      <meta name="description" content={metadata.description} />
      <meta name="keywords" content={metadata.keywords} />
      <meta name="author" content="Julia Jakubowicz" />
      <meta name="creator" content="Julia Jakubowicz" />
      <meta name="publisher" content="BAKASANA" />
      <meta name="copyright" content="© 2024 BAKASANA - Wszystkie prawa zastrzeżone" />
      <meta name="language" content="pl" />
      <meta name="revisit-after" content="7 days" />
      <meta name="rating" content="general" />
      <meta name="distribution" content="global" />
      <meta name="classification" content="Travel, Yoga, Wellness, Retreats" />
      <meta name="category" content="Yoga Retreats" />
      <meta name="coverage" content="Worldwide" />
      <meta name="target" content="adults" />
      <meta name="HandheldFriendly" content="True" />
      <meta name="MobileOptimized" content="320" />
      
      {/* ========================================
          ADVANCED ROBOTS & CRAWLING
      ======================================== */}
      <meta name="robots" content={robotsDirectives} />
      <meta name="googlebot" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="bingbot" content="index, follow" />
      <meta name="slurp" content="index, follow" />
      <meta name="duckduckbot" content="index, follow" />
      <meta name="facebookexternalhit" content="index, follow" />
      <meta name="twitterbot" content="index, follow" />
      <meta name="linkedinbot" content="index, follow" />
      <meta name="pinterestbot" content="index, follow" />
      <meta name="applebot" content="index, follow" />
      <meta name="yandexbot" content="index, follow" />
      <meta name="baiduspider" content="index, follow" />
      
      {/* ========================================
          CANONICAL & ALTERNATE LINKS
      ======================================== */}
      <link rel="canonical" href={metadata.alternates.canonical} />
      <link rel="alternate" href={metadata.alternates.canonical} hrefLang="pl" />
      <link rel="alternate" href={metadata.alternates.languages.en} hrefLang="en" />
      <link rel="alternate" href={metadata.alternates.canonical} hrefLang="x-default" />
      
      {/* ========================================
          OPEN GRAPH TAGS
      ======================================== */}
      <meta property="og:title" content={metadata.openGraph.title} />
      <meta property="og:description" content={metadata.openGraph.description} />
      <meta property="og:url" content={metadata.openGraph.url} />
      <meta property="og:type" content={metadata.openGraph.type} />
      <meta property="og:locale" content={metadata.openGraph.locale} />
      <meta property="og:locale:alternate" content={metadata.openGraph.alternateLocale} />
      <meta property="og:site_name" content={metadata.openGraph.siteName} />
      <meta property="og:updated_time" content={timestamp} />
      <meta property="article:author" content="Julia Jakubowicz" />
      <meta property="article:publisher" content="https://www.facebook.com/bakasana.yoga" />
      <meta property="article:section" content="Travel & Yoga" />
      <meta property="article:tag" content="yoga, retreats, bali, sri lanka, meditation" />
      
      {/* Open Graph Images */}
      {metadata.openGraph.images.map((image, index) => (
        <meta key={index} property="og:image" content={image.url} />
      ))}
      {metadata.openGraph.images.map((image, index) => (
        <meta key={`secure-${index}`} property="og:image:secure_url" content={image.url} />
      ))}
      {metadata.openGraph.images.map((image, index) => (
        <meta key={`alt-${index}`} property="og:image:alt" content={image.alt} />
      ))}
      {metadata.openGraph.images.map((image, index) => (
        <meta key={`width-${index}`} property="og:image:width" content={image.width} />
      ))}
      {metadata.openGraph.images.map((image, index) => (
        <meta key={`height-${index}`} property="og:image:height" content={image.height} />
      ))}
      {metadata.openGraph.images.map((image, index) => (
        <meta key={`type-${index}`} property="og:image:type" content={image.type} />
      ))}
      
      {/* ========================================
          TWITTER CARDS
      ======================================== */}
      <meta name="twitter:card" content={metadata.twitter.card} />
      <meta name="twitter:title" content={metadata.twitter.title} />
      <meta name="twitter:description" content={metadata.twitter.description} />
      <meta name="twitter:creator" content={metadata.twitter.creator} />
      <meta name="twitter:site" content={metadata.twitter.site} />
      <meta name="twitter:domain" content="bakasana-travel.blog" />
      <meta name="twitter:dnt" content="on" />
      <meta name="twitter:widgets:csp" content="on" />
      
      {/* Twitter Images */}
      {metadata.twitter.images.map((image, index) => (
        <meta key={index} name="twitter:image" content={image.url} />
      ))}
      {metadata.twitter.images.map((image, index) => (
        <meta key={`twitter-alt-${index}`} name="twitter:image:alt" content={image.alt} />
      ))}
      
      {/* ========================================
          MOBILE & WEBAPP OPTIMIZATION
      ======================================== */}
      <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes" />
      <meta name="theme-color" content="#FDFCF8" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="BAKASANA" />
      <meta name="application-name" content="BAKASANA" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="msapplication-TileColor" content="#C9A575" />
      <meta name="msapplication-TileImage" content="/images/icons/ms-tile-150x150.png" />
      <meta name="msapplication-config" content="/browserconfig.xml" />
      
      {/* ========================================
          PERFORMANCE & SECURITY
      ======================================== */}
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      <meta httpEquiv="x-dns-prefetch-control" content="on" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="format-detection" content="address=no" />
      <meta name="format-detection" content="email=no" />
      <meta name="referrer" content="strict-origin-when-cross-origin" />
      <meta name="color-scheme" content="light" />
      <meta name="supported-color-schemes" content="light" />
      
      {/* ========================================
          SEARCH ENGINE VERIFICATIONS
      ======================================== */}
      <meta name="google-site-verification" content={process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION} />
      <meta name="msvalidate.01" content={process.env.NEXT_PUBLIC_BING_VERIFICATION} />
      <meta name="yandex-verification" content={process.env.NEXT_PUBLIC_YANDEX_VERIFICATION} />
      <meta name="p:domain_verify" content={process.env.NEXT_PUBLIC_PINTEREST_VERIFICATION} />
      <meta name="facebook-domain-verification" content={process.env.NEXT_PUBLIC_FACEBOOK_VERIFICATION} />
      
      {/* ========================================
          PRECONNECT & DNS-PREFETCH
      ======================================== */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://www.googletagmanager.com" />
      <link rel="preconnect" href="https://cdn.mixpanel.com" />
      <link rel="preconnect" href="https://api-eu.mixpanel.com" />
      <link rel="preconnect" href="https://connect.facebook.net" />
      <link rel="preconnect" href="https://www.facebook.com" />
      <link rel="preconnect" href="https://snap.licdn.com" />
      <link rel="preconnect" href="https://static.hotjar.com" />
      <link rel="preconnect" href="https://www.clarity.ms" />
      <link rel="preconnect" href="https://cdn.sanity.io" />
      <link rel="preconnect" href="https://images.unsplash.com" />
      
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      <link rel="dns-prefetch" href="//cdn.mixpanel.com" />
      <link rel="dns-prefetch" href="//api-eu.mixpanel.com" />
      <link rel="dns-prefetch" href="//connect.facebook.net" />
      <link rel="dns-prefetch" href="//www.facebook.com" />
      <link rel="dns-prefetch" href="//snap.licdn.com" />
      <link rel="dns-prefetch" href="//static.hotjar.com" />
      <link rel="dns-prefetch" href="//www.clarity.ms" />
      <link rel="dns-prefetch" href="//cdn.sanity.io" />
      <link rel="dns-prefetch" href="//images.unsplash.com" />
      
      {/* ========================================
          FAVICON & ICONS
      ======================================== */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/images/icons/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/images/icons/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/apple-touch-icon.png" />
      <link rel="apple-touch-icon" sizes="152x152" href="/images/icons/apple-touch-icon-152x152.png" />
      <link rel="apple-touch-icon" sizes="144x144" href="/images/icons/apple-touch-icon-144x144.png" />
      <link rel="apple-touch-icon" sizes="120x120" href="/images/icons/apple-touch-icon-120x120.png" />
      <link rel="apple-touch-icon" sizes="114x114" href="/images/icons/apple-touch-icon-114x114.png" />
      <link rel="apple-touch-icon" sizes="76x76" href="/images/icons/apple-touch-icon-76x76.png" />
      <link rel="apple-touch-icon" sizes="72x72" href="/images/icons/apple-touch-icon-72x72.png" />
      <link rel="apple-touch-icon" sizes="60x60" href="/images/icons/apple-touch-icon-60x60.png" />
      <link rel="apple-touch-icon" sizes="57x57" href="/images/icons/apple-touch-icon-57x57.png" />
      
      {/* ========================================
          MANIFEST & PWA
      ======================================== */}
      <link rel="manifest" href="/manifest.json" />
      <meta name="msapplication-starturl" content="/" />
      <meta name="msapplication-window" content="width=1024;height=768" />
      <meta name="msapplication-navbutton-color" content="#C9A575" />
      <meta name="msapplication-tooltip" content="BAKASANA - Retreaty Jogi" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-touch-fullscreen" content="yes" />
      
      {/* ========================================
          STRUCTURED DATA
      ======================================== */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: structuredDataScript
        }}
      />
      
      {/* ========================================
          CRITICAL CSS
      ======================================== */}
      <style dangerouslySetInnerHTML={{ __html: criticalCSS }} />
      
      {/* ========================================
          PRELOAD CRITICAL RESOURCES
      ======================================== */}
      <link
        rel="preload"
        href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500&family=Cormorant+Garamond:wght@300;400&display=swap"
        as="style"
        onLoad="this.onload=null;this.rel='stylesheet'"
      />
      
      {/* Hero image preload */}
      <link
        rel="preload"
        href="/images/hero/bakasana-hero-2025.jpg"
        as="image"
        type="image/jpeg"
      />
      
      {/* Logo preload */}
      <link
        rel="preload"
        href="/images/logo/bakasana-logo-hd.png"
        as="image"
        type="image/png"
      />
      
      {/* ========================================
          RICH SNIPPETS HINTS
      ======================================== */}
      <meta property="product:price:amount" content="3400" />
      <meta property="product:price:currency" content="PLN" />
      <meta property="product:availability" content="in stock" />
      <meta property="product:condition" content="new" />
      <meta property="product:retailer_item_id" content="retreat-bali-2025" />
      <meta property="product:brand" content="BAKASANA" />
      <meta property="product:category" content="Travel > Yoga Retreats" />
      
      {/* ========================================
          SOCIAL MEDIA INTEGRATION
      ======================================== */}
      <meta property="fb:app_id" content={process.env.NEXT_PUBLIC_FACEBOOK_APP_ID} />
      <meta property="fb:admins" content={process.env.NEXT_PUBLIC_FACEBOOK_ADMIN_ID} />
      <meta property="fb:pages" content={process.env.NEXT_PUBLIC_FACEBOOK_PAGE_ID} />
      
      {/* ========================================
          ACCESSIBILITY & INTERNATIONALIZATION
      ======================================== */}
      <meta name="color-scheme" content="light" />
      <meta name="supported-color-schemes" content="light" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="skype_toolbar" content="skype_toolbar_parser_compatible" />
      
      {/* ========================================
          PERFORMANCE HINTS
      ======================================== */}
      <meta httpEquiv="Accept-CH" content="DPR, Width, Viewport-Width" />
      <meta name="supported-color-schemes" content="light" />
      <meta name="color-scheme" content="light" />
      
      {/* ========================================
          CUSTOM BUSINESS META
      ======================================== */}
      <meta name="geo.region" content="PL-14" />
      <meta name="geo.placename" content="Warszawa" />
      <meta name="geo.position" content="52.2297;21.0122" />
      <meta name="ICBM" content="52.2297, 21.0122" />
      <meta name="business:hours" content="Mo-Fr 09:00-18:00" />
      <meta name="business:contact_data:street_address" content="ul. Przykładowa 123" />
      <meta name="business:contact_data:locality" content="Warszawa" />
      <meta name="business:contact_data:postal_code" content="00-000" />
      <meta name="business:contact_data:country_name" content="Poland" />
      <meta name="business:contact_data:email" content="<EMAIL>" />
      <meta name="business:contact_data:phone_number" content="+**************" />
      <meta name="business:contact_data:website" content="https://bakasana-travel.blog" />
    </Head>
  );
}