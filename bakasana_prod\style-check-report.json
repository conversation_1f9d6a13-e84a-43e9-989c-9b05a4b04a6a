{"timestamp": "2025-07-23T21:33:34.412Z", "stats": {"totalFiles": 222, "checkedFiles": 222, "issues": 278, "score": 37}, "issues": [{"file": "app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx", "line": 7, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\blog\\page.jsx", "line": 39, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 13, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\error.jsx", "line": 28, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\error.jsx", "line": 31, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\galeria\\page.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 124, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 213, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 240, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 322, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 340, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 344, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 348, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 106, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 161, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 190, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 232, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 306, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 7, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 39, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 45, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 124, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 213, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 245, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 289, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 383, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 423, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 7, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\kontakt\\ContactForm.jsx", "line": 250, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\kontakt\\page.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\kontakt\\page.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 14, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 15, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 16, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 17, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 18, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 19, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 20, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 22, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 23, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\mapa\\page.jsx", "line": 17, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\mapa\\page.jsx", "line": 110, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\mapa\\page.jsx", "line": 146, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\mapa\\page.jsx", "line": 196, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\not-found-bali.jsx", "line": 19, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\not-found-bali.jsx", "line": 32, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\o-mnie\\page.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\page.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\page.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\page.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\page.jsx", "line": 7, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\program\\page.jsx", "line": 454, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\program\\page.jsx", "line": 534, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\program\\page.jsx", "line": 575, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\program\\page.jsx", "line": 682, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\program\\page.jsx", "line": 683, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\program\\page.jsx", "line": 435, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\page.jsx", "line": 520, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\page.jsx", "line": 561, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\retreaty\\page.jsx", "line": 8, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 93, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 148, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 178, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 272, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 310, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\rezerwacja\\page.jsx", "line": 36, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\rezerwacja\\page.jsx", "line": 140, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 35, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 41, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 47, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 58, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 64, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 76, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 176, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 202, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 470, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 29, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 39, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 170, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 193, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 197, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 338, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 342, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 435, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 518, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\zajecia-stacjonarne\\page.jsx", "line": 112, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\zajecia-stacjonarne\\page.jsx", "line": 184, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\zajecia-stacjonarne\\page.jsx", "line": 234, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\zajecia-stacjonarne\\page.jsx", "line": 277, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\zajecia-stacjonarne\\page.jsx", "line": 287, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\zajecia-stacjonarne\\page.jsx", "line": 297, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\zajecia-stacjonarne\\page.jsx", "line": 307, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\zajecia-stacjonarne\\page.jsx", "line": 354, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\zajecia-stacjonarne\\page.jsx", "line": 507, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\About\\OldMoneyAbout.tsx", "line": 47, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\About\\OldMoneyAbout.tsx", "line": 64, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\About\\OldMoneyAbout.tsx", "line": 2, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\BlogWhatsAppCTA.jsx", "line": 46, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 437, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 505, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 855, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 870, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 887, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 897, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 907, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 955, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 957, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 989, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 874, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\BookingCalendar.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\BookingForm.jsx", "line": 453, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\ClientInteractiveButton.jsx", "line": 46, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ClientInteractiveButton.jsx", "line": 58, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ClientOnlyResponsiveChecker.jsx", "line": 79, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ClientOnlyResponsiveChecker.jsx", "line": 87, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ErrorBoundary\\AdvancedErrorBoundary.jsx", "line": 92, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\Events\\EventCard.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Events\\EventCard.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\FAQSection.jsx", "line": 69, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\FitsseyIntegration.jsx", "line": 62, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\FitsseyIntegration.jsx", "line": 131, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\FitsseyIntegration.jsx", "line": 62, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\FitsseyIntegration.jsx", "line": 74, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\FitsseyIntegration.jsx", "line": 126, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\FitsseyIntegration.jsx", "line": 140, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\FitsseyIntegration.jsx", "line": 154, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\FitsseySchedule.jsx", "line": 132, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\FitsseySchedule.jsx", "line": 151, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\FitsseySchedule.jsx", "line": 212, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\FitsseySchedule.jsx", "line": 248, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Footer\\OldMoneyFooter.tsx", "line": 2, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Footer\\OldMoneyFooter.tsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Footer\\OldMoneyFooter.tsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\GhostNavbar.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\GhostNavbar.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Hero\\OldMoneyHero.tsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\HighlightCard\\index.jsx", "line": 15, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Home\\HeroVariantsDemo.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Home\\ProfessionalHero.jsx", "line": 41, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\Home\\WellnessPage.jsx", "line": 8, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\InteractiveMap.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\InteractiveMap.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\InteractiveMap.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\InternalLinks.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\InternalLinks.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\MinimalistHero.jsx", "line": 103, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\MinimalistHero.jsx", "line": 104, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\MinimalistHero.jsx", "line": 105, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\MinimalistHero.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\MinimalistHero.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\MinimalistHero.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Navbar\\ClientNavbar.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Navigation\\OldMoneyNavbar.tsx", "line": 70, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\OldMoneyNavbar.tsx", "line": 117, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\OldMoneyNavbar.tsx", "line": 118, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\OldMoneyNavbar.tsx", "line": 119, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\OldMoneyNavbar.tsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Navigation\\OldMoneyNavbar.tsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\NewsletterSignup.jsx", "line": 87, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\NewsletterSignup.jsx", "line": 137, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\NewsletterSignup.jsx", "line": 162, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\OptimizedImage.jsx", "line": 160, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PerfectNavbar.jsx", "line": 195, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PerfectNavbar.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 28, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 58, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 131, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 132, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 133, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 174, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 175, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 177, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 178, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 181, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 182, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 185, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 186, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 198, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 200, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 201, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 206, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 207, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 219, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 235, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 239, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 244, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PerformantWhatsApp.jsx", "line": 97, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 217, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 219, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 221, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 223, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 262, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 264, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 318, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 360, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 364, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 368, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 372, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\ResponsiveChecker.jsx", "line": 73, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ResponsiveChecker.jsx", "line": 81, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\RetreatCalendar.jsx", "line": 20, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\SanityRetreats.jsx", "line": 229, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\SanityRetreats.jsx", "line": 267, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\SanityTestimonials.jsx", "line": 115, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\ScrollReveal.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Services\\OldMoneyServices.tsx", "line": 100, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Services\\OldMoneyServices.tsx", "line": 152, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Services\\OldMoneyServices.tsx", "line": 2, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Services\\OldMoneyServices.tsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\TransformationCTA.jsx", "line": 14, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 1, "type": "icon-system", "message": "File uses legacy icon imports", "suggestion": "Migrate to unified Icon system"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 287, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 328, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 330, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 375, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 383, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 390, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 397, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 404, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Travel\\DestinationCard.jsx", "line": 157, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\DestinationCard.jsx", "line": 165, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\DestinationCard.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Travel\\InteractiveMap.jsx", "line": 215, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\InteractiveMap.jsx", "line": 218, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 153, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 166, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 179, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 195, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 384, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 421, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 142, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 144, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 145, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 156, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 230, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 234, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 238, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 249, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 255, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 261, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 299, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 322, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 329, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 336, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 342, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ui\\BlogComponents.jsx", "line": 349, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\ui\\EnhancedButton.jsx", "line": 170, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ui\\ErrorBoundary.jsx", "line": 85, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ui\\ErrorBoundary.jsx", "line": 142, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ui\\ErrorBoundary.jsx", "line": 165, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ui\\ErrorBoundary.jsx", "line": 191, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ui\\ErrorBoundary.jsx", "line": 194, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ui\\ErrorBoundary.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\ui\\ErrorBoundary.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\ui\\IconSystem.jsx", "line": 1, "type": "icon-system", "message": "File uses legacy icon imports", "suggestion": "Migrate to unified Icon system"}, {"file": "components\\ui\\LazyImage.jsx", "line": 143, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ui\\OptimizedLazyImage.jsx", "line": 191, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ui\\PageLoader.jsx", "line": 17, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ui\\PageTransition.jsx", "line": 251, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ui\\PageTransition.jsx", "line": 2, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\ui\\PageTransition.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\ui\\QualityAssurance.jsx", "line": 235, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ui\\QualityAssuranceWrapper.jsx", "line": 22, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ui\\Section.jsx", "line": 129, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ui\\Section.jsx", "line": 106, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\ui\\SkeletonLoader.jsx", "line": 30, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ui\\SkeletonLoader.jsx", "line": 120, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\WorldClassDesign\\SmartPreloader.jsx", "line": 127, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "data\\blogPosts.js", "line": 1, "type": "icon-system", "message": "File uses legacy icon imports", "suggestion": "Migrate to unified Icon system"}, {"file": "data\\contactData.js", "line": 1, "type": "icon-system", "message": "File uses legacy icon imports", "suggestion": "Migrate to unified Icon system"}, {"file": "data\\eventData.js", "line": 1, "type": "icon-system", "message": "File uses legacy icon imports", "suggestion": "Migrate to unified Icon system"}], "summary": {"score": 37, "totalIssues": 278, "issuesByType": {"import-order": 61, "rounded-elements": 37, "hardcoded-typography": 47, "hardcoded-icon": 128, "icon-system": 5}}}