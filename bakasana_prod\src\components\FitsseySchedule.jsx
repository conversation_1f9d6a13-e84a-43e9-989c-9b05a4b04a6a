
'use client';
import React, { useState, useEffect } from 'react';

import { CardTitle, BodyText  } from '@/components/ui/UnifiedTypography';
import { Icon  } from '@/components/ui/IconSystem';
import { ServiceCard  } from '@/components/ui/UnifiedCard';

import FitsseyIntegration from './FitsseyIntegration';

export function FitsseySessionCard({ session, showPrice = true, showAvailability = true }) {



// Mock data - w przyszłości można to zastąpić API call do Fitssey
const mockScheduleData = [
  {
    id: 1,
    day: 'Poniedziałek',
    time: '18:00',
    duration: 75,
    type: 'Hatha Yoga',
    instructor: '<PERSON>',
    level: 'Wszystkie poziomy',
    spotsAvailable: 8,
    totalSpots: 12,
    price: 60
  },
  {
    id: 2,
    day: 'Wtorek',
    time: '19:30',
    duration: 60,
    type: 'Vinyasa Flow',
    instructor: '<PERSON>',
    level: 'Średniozaawansowany',
    spotsAvailable: 3,
    totalSpots: 10,
    price: 60
  },
  {
    id: 3,
    day: 'Środa',
    time: '17:00',
    duration: 60,
    type: 'Yoga Terapeutyczna',
    instructor: '<PERSON> <PERSON>akubowicz',
    level: 'Terapeutyczny',
    spotsAvailable: 6,
    totalSpots: 8,
    price: 70
  },
  {
    id: 4,
    day: 'Czwartek',
    time: '19:00',
    duration: 90,
    type: 'Yin Yoga',
    instructor: 'Julia Jakubowicz',
    level: 'Wszystkie poziomy',
    spotsAvailable: 10,
    totalSpots: 12,
    price: 60
  },
  {
    id: 5,
    day: 'Piątek',
    time: '18:30',
    duration: 60,
    type: 'Vinyasa Flow',
    instructor: 'Julia Jakubowicz',
    level: 'Średniozaawansowany',
    spotsAvailable: 0,
    totalSpots: 10,
    price: 60
  },
  {
    id: 6,
    day: 'Sobota',
    time: '10:00',
    duration: 75,
    type: 'Hatha Yoga',
    instructor: 'Julia Jakubowicz',
    level: 'Wszystkie poziomy',
    spotsAvailable: 12,
    totalSpots: 12,
    price: 60
  },
  {
    id: 7,
    day: 'Niedziela',
    time: '11:00',
    duration: 90,
    type: 'Yin Yoga',
    instructor: 'Julia Jakubowicz',
    level: 'Wszystkie poziomy',
    spotsAvailable: 8,
    totalSpots: 12,
    price: 60
  }
];

  showPrices = true,
  showAvailability = true,
  compact = false,
  className = ""
}) {
  const [scheduleData, setScheduleData] = useState(mockScheduleData);
  const [loading, setLoading] = useState(false);

  // W przyszłości można dodać fetch z API Fitssey
  useEffect(() => {
    // fetchScheduleFromFitssey();
  }, []);

  const getAvailabilityStatus = (available, total) => {
    const percentage = (available / total) * 100;
    if (available === 0) return { status: 'full', color: 'text-red-600', bg: 'bg-red-50' };
    if (percentage <= 30) return { status: 'limited', color: 'text-amber-600', bg: 'bg-amber-50' };
    return { status: 'available', color: 'text-green-600', bg: 'bg-green-50' };
  };

  const getAvailabilityText = (available, total) => {
    if (available === 0) return 'Wyprzedane';
    if (available <= 3) return `Ostatnie ${available} miejsca`;
    return `${available} miejsc dostępnych`;
  };

  if (compact) {
    return (
      <div className={`fitssey-schedule-compact ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {scheduleData.map((session) => {
            const availability = getAvailabilityStatus(session.spotsAvailable, session.totalSpots);
            
            return (
              <div key={session.id} className="bg-white rounded-lg p-4 shadow-sm border border-stone-light">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <div className="font-medium text-enterprise-brown text-sm">{session.day}</div>
                    <div className="text-lg font-bold text-charcoal">{session.time}</div>
                  </div>
                  {showPrices && (
                    <div className="text-right">
                      <div className="text-sm font-medium text-charcoal">{session.price} PLN</div>
                    </div>
                  )}
                </div>
                
                <div className="mb-2">
                  <div className="text-sm font-medium text-charcoal">{session.type}</div>
                  <div className="text-xs text-sage">{session.duration} min • {session.level}</div>
                </div>

                {showAvailability && (
                  <div className={`text-xs px-2 py-1 rounded-full inline-block mb-3 ${availability.bg} ${availability.color}`}>
                    {getAvailabilityText(session.spotsAvailable, session.totalSpots)}
                  </div>
                )}

                <FitsseyIntegration
                  buttonText={session.spotsAvailable === 0 ? "Lista oczekujących" : "Zapisz się"}
                  variant={session.spotsAvailable === 0 ? "secondary" : "primary"}
                  size="sm"
                  trackingEvent={`schedule_signup_${session.type.toLowerCase().replace(' ', '_')}`}
                  className="w-full"
                />
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  return (
    <div className={`fitssey-schedule ${className}`}>
      <div className="space-y-4">
        {scheduleData.map((session) => {
          const availability = getAvailabilityStatus(session.spotsAvailable, session.totalSpots);
          
          return (
            <ServiceCard key={session.id} className="p-6">
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-4 mb-2">
                    <div>
                      <CardTitle className="text-enterprise-brown mb-1">
                        {session.day} • {session.time}
                      </CardTitle>
                      <BodyText className="text-charcoal-light text-sm">
                        {session.type} • {session.duration} min
                      </BodyText>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-sage">
                    <div className="flex items-center gap-1">
                      <Icon name="user" size="xs" />
                      {session.instructor}
                    </div>
                    <div className="flex items-center gap-1">
                      <Icon name="target" size="xs" />
                      {session.level}
                    </div>
                    {showPrices && (
                      <div className="flex items-center gap-1">
                        <Icon name="credit-card" size="xs" />
                        {session.price} PLN
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  {showAvailability && (
                    <div className={`px-3 py-1 rounded-full text-sm ${availability.bg} ${availability.color}`}>
                      {getAvailabilityText(session.spotsAvailable, session.totalSpots)}
                    </div>
                  )}
                  
                  <FitsseyIntegration
                    buttonText={session.spotsAvailable === 0 ? "Lista oczekujących" : "Zapisz się"}
                    variant={session.spotsAvailable === 0 ? "secondary" : "primary"}
                    size="md"
                    trackingEvent={`schedule_signup_${session.type.toLowerCase().replace(' ', '_')}`}
                  />
                </div>
              </div>
            </ServiceCard>
          );
        })}
      </div>
    </div>
  );
}

// Komponent dla pojedynczej sesji
  const availability = getAvailabilityStatus(session.spotsAvailable, session.totalSpots);
  
  return (
    <ServiceCard className="p-4">
      <div className="text-center">
        <CardTitle className="text-enterprise-brown mb-2">
          {session.type}
        </CardTitle>
        <BodyText className="text-sage mb-2">
          {session.day} • {session.time} • {session.duration} min
        </BodyText>
        
        {showAvailability && (
          <div className={`text-xs px-2 py-1 rounded-full inline-block mb-3 ${availability.bg} ${availability.color}`}>
            {getAvailabilityText(session.spotsAvailable, session.totalSpots)}
          </div>
        )}
        
        {showPrice && (
          <div className="text-lg font-bold text-charcoal mb-3">
            {session.price} PLN
          </div>
        )}
        
        <FitsseyIntegration
          buttonText={session.spotsAvailable === 0 ? "Lista oczekujących" : "Zapisz się"}
          variant={session.spotsAvailable === 0 ? "secondary" : "primary"}
          size="md"
          trackingEvent={`session_signup_${session.type.toLowerCase().replace(' ', '_')}`}
          className="w-full"
        />
      </div>
    </ServiceCard>
  );
}

function getAvailabilityStatus(available, total) {
  const percentage = (available / total) * 100;
  if (available === 0) return { status: 'full', color: 'text-red-600', bg: 'bg-red-50' };
  if (percentage <= 30) return { status: 'limited', color: 'text-amber-600', bg: 'bg-amber-50' };
  return { status: 'available', color: 'text-green-600', bg: 'bg-green-50' };
}

function getAvailabilityText(available, total) {
  if (available === 0) return 'Wyprzedane';
  if (available <= 3) return `Ostatnie ${available} miejsca`;
  return `${available} miejsc dostępnych`;
}

export default function FitsseySchedule({ 
